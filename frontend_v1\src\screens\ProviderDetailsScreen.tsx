/**
 * Provider Details Screen - Comprehensive Provider Information Display
 *
 * Component Contract:
 * - Displays detailed provider information including services, contact info, and reviews
 * - Supports booking services and contacting providers
 * - Implements modern UI/UX with responsive design
 * - Follows accessibility guidelines and testing standards
 * - Integrates with navigation and state management
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image } from 'react-native';
import { Alert, Linking, ActivityIndicator,  } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Box } from '../components/atoms/Box';
import { Button } from '../components/atoms/Button';
import { IconButton } from '../components/atoms/IconButton';
import { SafeAreaWrapper } from '../components/ui/SafeAreaWrapper';
import { HeaderHelpButton } from '../components/help';
import { ALL_SERVICE_PROVIDERS } from '../config/testAccounts';
import { useTheme } from '../contexts/ThemeContext';
import type { CustomerStackParamList } from '../navigation/types';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../utils/responsiveUtils';
import EnhancedProviderProfile, {
  EnhancedProvider,
  PortfolioItem,
  Certification,
  ServiceOffering
} from '../components/providers/EnhancedProviderProfile';

// Debug: Check if import is working
console.log(
  '🔍 ProviderDetailsScreen: ALL_SERVICE_PROVIDERS imported:',
  !!ALL_SERVICE_PROVIDERS,
);
console.log(
  '🔍 ProviderDetailsScreen: ALL_SERVICE_PROVIDERS length:',
  ALL_SERVICE_PROVIDERS?.length,
);

// Transform basic provider data to enhanced provider format
const transformToEnhancedProvider = (basicProvider: any): EnhancedProvider => {
  return {
    id: basicProvider.id,
    businessName: basicProvider.businessName || basicProvider.name,
    bio: basicProvider.bio || `Professional ${basicProvider.services?.[0]?.category || 'service'} provider with years of experience delivering exceptional results.`,
    profileImage: basicProvider.profileImage || 'https://via.placeholder.com/150x150',
    coverImage: basicProvider.coverImage || 'https://via.placeholder.com/400x200',
    rating: basicProvider.rating || 4.5,
    reviewCount: basicProvider.reviewCount || 127,
    completedJobs: basicProvider.completedJobs || 250,
    yearsOfExperience: basicProvider.yearsOfExperience || 5,
    responseTime: basicProvider.responseTime || '< 1 hour',
    location: {
      address: basicProvider.location?.address || '123 Main St',
      city: basicProvider.location?.city || 'Toronto',
      distance: basicProvider.location?.distance || '2.5 km',
    },
    contact: {
      phone: basicProvider.contact?.phone || '+1 (*************',
      email: basicProvider.contact?.email || '<EMAIL>',
      website: basicProvider.contact?.website,
    },
    portfolio: generateSamplePortfolio(basicProvider),
    certifications: generateSampleCertifications(basicProvider),
    services: transformServices(basicProvider.services || []),
    specialties: basicProvider.specialties || ['Professional Service', 'Quality Work', 'Customer Satisfaction'],
    languages: basicProvider.languages || ['English', 'French'],
    workingHours: basicProvider.workingHours || {
      monday: { open: '09:00', close: '17:00', isOpen: true },
      tuesday: { open: '09:00', close: '17:00', isOpen: true },
      wednesday: { open: '09:00', close: '17:00', isOpen: true },
      thursday: { open: '09:00', close: '17:00', isOpen: true },
      friday: { open: '09:00', close: '17:00', isOpen: true },
      saturday: { open: '10:00', close: '16:00', isOpen: true },
      sunday: { open: '10:00', close: '16:00', isOpen: false },
    },
    policies: {
      cancellation: '24 hours notice required for cancellations',
      rescheduling: 'Free rescheduling up to 2 hours before appointment',
      payment: ['Credit Card', 'Debit Card', 'Cash', 'E-Transfer'],
    },
    verification: {
      isVerified: basicProvider.verified || true,
      verifiedBadges: ['Identity Verified', 'Background Check', 'Insurance Verified'],
      backgroundCheck: true,
      insurance: true,
    },
  };
};

const generateSamplePortfolio = (provider: any): PortfolioItem[] => {
  return [
    {
      id: '1',
      title: 'Recent Project Showcase',
      description: 'High-quality service delivery with exceptional attention to detail and customer satisfaction.',
      images: ['https://via.placeholder.com/300x200', 'https://via.placeholder.com/300x200'],
      category: provider.services?.[0]?.category || 'Professional Service',
      completedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      clientTestimonial: 'Outstanding work! Exceeded all expectations and delivered on time.',
    },
    {
      id: '2',
      title: 'Premium Service Example',
      description: 'Demonstration of premium service capabilities with innovative solutions.',
      images: ['https://via.placeholder.com/300x200'],
      category: provider.services?.[0]?.category || 'Professional Service',
      completedAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
    },
  ];
};

const generateSampleCertifications = (provider: any): Certification[] => {
  return [
    {
      id: '1',
      name: 'Professional Service Certification',
      issuer: 'Industry Standards Board',
      dateObtained: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
      level: 'advanced',
      badgeImage: 'https://via.placeholder.com/100x100',
    },
    {
      id: '2',
      name: 'Quality Assurance Certificate',
      issuer: 'Quality Standards Institute',
      dateObtained: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString(),
      level: 'expert',
      credentialId: 'QSI-2024-001',
    },
  ];
};

const transformServices = (services: any[]): ServiceOffering[] => {
  return services.map((service, index) => ({
    id: service.id || `service-${index}`,
    name: service.name || service.title || 'Professional Service',
    description: service.description || 'High-quality professional service tailored to your needs.',
    category: service.category || 'Professional Services',
    duration: service.duration || 60,
    price: {
      min: service.price?.min || service.price || 50,
      max: service.price?.max || (service.price ? service.price * 1.5 : 100),
      currency: 'CAD',
    },
    images: service.images || ['https://via.placeholder.com/300x200'],
    features: service.features || ['Professional Quality', 'Satisfaction Guaranteed', 'Flexible Scheduling'],
    addOns: service.addOns || [
      { id: '1', name: 'Express Service', price: 25, description: 'Priority scheduling and faster completion' },
      { id: '2', name: 'Premium Package', price: 50, description: 'Enhanced service with additional benefits' },
    ],
    availability: {
      daysOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
      timeSlots: ['09:00', '10:00', '11:00', '14:00', '15:00', '16:00'],
    },
    requirements: service.requirements || ['Valid ID required', 'Please arrive 5 minutes early'],
    cancellationPolicy: '24 hours notice required for cancellations',
  }));
};

// Types
interface ServiceProvider {
  id: string;
  business_name: string;
  description: string;
  rating: number;
  review_count: number;
  is_active: boolean;
  is_verified: boolean;
  city: string;
  state?: string;
  distance?: string;
  avatar?: string | null;
  categories: string[];
  business_phone?: string;
  business_email?: string;
  address?: string;
  operating_hours?: {
    [key: string]: { open: string; close: string; isOpen: boolean };
  };
  services?: Array<{
    id: string;
    name: string;
    description: string;
    price: number;
    duration: number;
    category: string;
  }>;
  portfolio_images?: string[];
}

interface Service {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number;
  category: string;
}

type ProviderDetailsScreenRouteProp = RouteProp<
  CustomerStackParamList,
  'ProviderDetails'
>;
type ProviderDetailsScreenNavigationProp = StackNavigationProp<
  CustomerStackParamList,
  'ProviderDetails'
>;

export const ProviderDetailsScreen: React.FC = () => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const navigation = useNavigation<ProviderDetailsScreenNavigationProp>();
  const route = useRoute<ProviderDetailsScreenRouteProp>();
  const { providerId } = route.params;

  const [provider, setProvider] = useState<ServiceProvider | null>(null);
  const [loading, setLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [activeTab, setActiveTab] = useState<'services' | 'reviews' | 'about' | 'portfolio'>('services');
  const [showAllReviews, setShowAllReviews] = useState(false);

  useEffect(() => {
    console.log('🔍 useEffect called with providerId:', providerId);
    loadProviderDetails();
  }, [providerId]);

  const loadProviderDetails = async () => {
    console.log('🔍 loadProviderDetails function called');
    setLoading(true);
    try {
      // Simulate API call - shorter delay for tests
      const delay = process.env.NODE_ENV === 'test' ? 100 : 1000;
      await new Promise(resolve => setTimeout(resolve, delay));

      // Debug logging
      console.log('🔍 Provider ID:', providerId);
      console.log(
        '🔍 ALL_SERVICE_PROVIDERS type:',
        typeof ALL_SERVICE_PROVIDERS,
      );
      console.log(
        '🔍 ALL_SERVICE_PROVIDERS length:',
        ALL_SERVICE_PROVIDERS?.length,
      );

      // Find provider from test accounts
      if (!ALL_SERVICE_PROVIDERS || !Array.isArray(ALL_SERVICE_PROVIDERS)) {
        console.error(
          '❌ Service providers data not available:',
          ALL_SERVICE_PROVIDERS,
        );
        throw new Error('Service providers data not available');
      }

      console.log('🔍 Looking for provider with ID:', providerId);
      console.log(
        '🔍 Available provider IDs:',
        ALL_SERVICE_PROVIDERS.map((account, index) => `provider_${index + 1}`),
      );

      const testAccount = ALL_SERVICE_PROVIDERS.find(
        account =>
          `provider_${ALL_SERVICE_PROVIDERS.indexOf(account) + 1}` ===
          providerId,
      );

      console.log('🔍 Found test account:', !!testAccount);

      if (testAccount) {
        const mockProvider: ServiceProvider = {
          id: providerId,
          business_name: `${testAccount.firstName} ${testAccount.lastName} ${testAccount.category}`,
          description:
            testAccount.description || 'Professional beauty services',
          rating: 4.2 + Math.random() * 0.8,
          review_count: Math.floor(Math.random() * 50) + 10,
          is_active: true,
          is_verified: Math.random() > 0.3,
          city: testAccount.city || 'Ottawa',
          state: 'ON',
          distance: `${(Math.random() * 10 + 1).toFixed(1)} km`,
          avatar: null,
          categories: [testAccount.category || 'Beauty Services'],
          business_phone: '(*************',
          business_email: testAccount.email,
          address: '123 Main Street, Ottawa, ON K1A 0A6',
          operating_hours: {
            monday: { open: '09:00', close: '17:00', isOpen: true },
            tuesday: { open: '09:00', close: '17:00', isOpen: true },
            wednesday: { open: '09:00', close: '17:00', isOpen: true },
            thursday: { open: '09:00', close: '18:00', isOpen: true },
            friday: { open: '09:00', close: '18:00', isOpen: true },
            saturday: { open: '10:00', close: '16:00', isOpen: true },
            sunday: { open: '11:00', close: '15:00', isOpen: false },
          },
          services: generateMockServices(
            testAccount.category || 'Beauty Services',
          ),
          portfolio_images: [
            'https://images.unsplash.com/photo-**********-138dadb4c035?w=400',
            'https://images.unsplash.com/photo-*************-8b13dee7a37e?w=400',
            'https://images.unsplash.com/photo-*************-e7ab37603c6f?w=400',
          ],
        };

        setProvider(mockProvider);
      } else {
        Alert.alert('Error', 'Provider not found');
        navigation.goBack();
      }
    } catch (error) {
      console.error('Failed to load provider details:', error);
      Alert.alert('Error', 'Failed to load provider details');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const generateMockServices = (category: string): Service[] => {
    const servicesByCategory: { [key: string]: Service[] } = {
      'Barber': [
        {
          id: '1',
          name: 'Classic Haircut',
          description: 'Traditional barbershop cut',
          price: 35,
          duration: 45,
          category: 'Barber',
        },
        {
          id: '2',
          name: 'Beard Trim',
          description: 'Professional beard trimming',
          price: 25,
          duration: 30,
          category: 'Barber',
        },
        {
          id: '3',
          name: 'Hot Towel Shave',
          description: 'Traditional straight razor shave',
          price: 40,
          duration: 45,
          category: 'Barber',
        },
        {
          id: '4',
          name: 'Beard Styling',
          description: 'Beard shaping and styling',
          price: 30,
          duration: 30,
          category: 'Barber',
        },
      ],
      'Salon': [
        {
          id: '5',
          name: 'Hair Color',
          description: 'Full color treatment',
          price: 120,
          duration: 120,
          category: 'Salon',
        },
        {
          id: '6',
          name: 'Highlights',
          description: 'Partial highlights',
          price: 95,
          duration: 90,
          category: 'Salon',
        },
        {
          id: '7',
          name: 'Blowout',
          description: 'Professional styling',
          price: 65,
          duration: 60,
          category: 'Salon',
        },
        {
          id: '8',
          name: 'Hair Treatment',
          description: 'Deep conditioning treatment',
          price: 75,
          duration: 75,
          category: 'Salon',
        },
      ],
      'Nail Services': [
        {
          id: '5',
          name: 'Manicure',
          description: 'Classic manicure',
          price: 35,
          duration: 45,
          category: 'Nails',
        },
        {
          id: '6',
          name: 'Pedicure',
          description: 'Relaxing pedicure',
          price: 45,
          duration: 60,
          category: 'Nails',
        },
        {
          id: '7',
          name: 'Gel Polish',
          description: 'Long-lasting gel polish',
          price: 25,
          duration: 30,
          category: 'Nails',
        },
        {
          id: '8',
          name: 'Nail Art',
          description: 'Custom nail designs',
          price: 15,
          duration: 20,
          category: 'Nails',
        },
      ],
      'Lash Services': [
        {
          id: '9',
          name: 'Lash Extensions',
          description: 'Individual lash extensions',
          price: 150,
          duration: 120,
          category: 'Lashes',
        },
        {
          id: '10',
          name: 'Lash Fill',
          description: 'Extension maintenance',
          price: 75,
          duration: 60,
          category: 'Lashes',
        },
        {
          id: '11',
          name: 'Lash Lift',
          description: 'Natural lash enhancement',
          price: 85,
          duration: 75,
          category: 'Lashes',
        },
      ],
    };

    return servicesByCategory[category] || servicesByCategory['Barber'];
  };

  const handleToggleFavorite = () => {
    setIsFavorite(!isFavorite);
    // TODO: Implement favorite functionality with backend
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Text key={i} style={styles.star}>
          ★
        </Text>,
      );
    }

    if (hasHalfStar) {
      stars.push(
        <Text key="half" style={styles.star}>
          ☆
        </Text>,
      );
    }

    return stars;
  };

  if (loading) {
    return (
      <SafeAreaWrapper style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.sage600} />
          <Text style={styles.loadingText}>Loading provider details...</Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  if (!provider) {
    return (
      <SafeAreaWrapper style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Provider not found</Text>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
            variant="primary"
          />
        </View>
      </SafeAreaWrapper>
    );
  }

  const filteredServices =
    selectedCategory === 'all'
      ? provider.services || []
      : (provider.services || []).filter(
          service =>
            service.category.toLowerCase() === selectedCategory.toLowerCase(),
        );

  return (
    <SafeAreaWrapper style={styles.container} testID="provider-details-screen">
      {/* Header */}
      <View style={styles.header}>
        <IconButton
          icon="arrow-back"
          onPress={() => navigation.goBack()}
          style={styles.backButton}
          testID="back-button"
          accessibilityLabel="Go back"
        />
        <View style={styles.headerActions}>
          <HeaderHelpButton
            size="medium"
            testID="provider-details-help-button"
          />
          <IconButton
            icon={isFavorite ? 'heart' : 'heart-outline'}
            onPress={handleToggleFavorite}
            style={styles.favoriteButton}
            testID="favorite-button"
            accessibilityLabel={
              isFavorite ? 'Remove from favorites' : 'Add to favorites'
            }
          />
        </View>
      </View>

      {/* Enhanced Provider Profile */}
      <EnhancedProviderProfile
        provider={transformToEnhancedProvider(provider)}
        onBookService={(service) => {
          navigation.navigate('Booking', {
            providerId: provider.id,
            serviceId: service.id,
            serviceName: service.name,
            servicePrice: service.price.min,
            serviceDuration: service.duration,
          });
        }}
        onContactProvider={() => {
          Alert.alert(
            'Contact Provider',
            'Choose how you would like to contact this provider:',
            [
              {
                text: 'Call',
                onPress: () => {
                  const phone = provider.contact?.phone || '+1 (*************';
                  Linking.openURL(`tel:${phone}`);
                },
              },
              {
                text: 'Email',
                onPress: () => {
                  const email = provider.contact?.email || '<EMAIL>';
                  Linking.openURL(`mailto:${email}`);
                },
              },
              {
                text: 'Message',
                onPress: () => {
                  navigation.navigate('Chat', {
                    providerId: provider.id,
                    providerName: provider.businessName || provider.name,
                  });
                },
              },
              { text: 'Cancel', style: 'cancel' },
            ]
          );
        }}
        onViewPortfolioItem={(item) => {
          Alert.alert('Portfolio Item', item.description);
        }}
        onViewCertification={(cert) => {
          Alert.alert('Certification', `${cert.name} - ${cert.issuer}`);
        }}
      />
    </SafeAreaWrapper>
  );
};

// Styles
const createStyles = (colors: any) => ({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: getResponsiveSpacing(20),
  },
  loadingText: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
    marginTop: getResponsiveSpacing(12),
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: getResponsiveSpacing(20),
  },
  errorText: {
    fontSize: getResponsiveFontSize(18),
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(20),
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(12),
    backgroundColor: colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: getResponsiveSpacing(8),
  },
  backButton: {
    backgroundColor: colors.background.secondary,
    borderRadius: 20,
    width: 40,
    height: 40,
  },
  favoriteButton: {
    backgroundColor: colors.background.secondary,
    borderRadius: 20,
    width: 40,
    height: 40,
  },
  content: {
    flex: 1,
  },
  providerHeader: {
    padding: getResponsiveSpacing(20),
    backgroundColor: colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },
  providerImageContainer: {
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(16),
    position: 'relative',
  },
  providerImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.background.secondary,
  },
  placeholderImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.sage200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: getResponsiveFontSize(36),
    fontWeight: '700',
    color: colors.sage700,
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: colors.sage600,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.background.primary,
  },
  verifiedText: {
    color: colors.text.onPrimary,
    fontSize: getResponsiveFontSize(12),
    fontWeight: '700',
  },
  providerInfo: {
    alignItems: 'center',
  },
  businessName: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: '700',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: getResponsiveSpacing(8),
  },
  description: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: getResponsiveSpacing(12),
    lineHeight: getResponsiveFontSize(22),
  },
  ratingContainer: {
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(8),
  },
  stars: {
    flexDirection: 'row',
    marginBottom: getResponsiveSpacing(4),
  },
  star: {
    fontSize: getResponsiveFontSize(18),
    color: '#FFD700',
    marginHorizontal: 1,
  },
  ratingText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
  },
  location: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
    textAlign: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: getResponsiveSpacing(20),
    paddingVertical: getResponsiveSpacing(16),
    gap: getResponsiveSpacing(12),
  },
  actionButton: {
    flex: 1,
  },
  section: {
    margin: getResponsiveSpacing(16),
    padding: getResponsiveSpacing(20),
    backgroundColor: colors.background.primary,
    borderRadius: getResponsiveSpacing(12),
    shadowColor: colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(16),
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(12),
  },
  contactIcon: {
    fontSize: getResponsiveFontSize(16),
    marginRight: getResponsiveSpacing(12),
    width: 20,
  },
  contactText: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.primary,
    flex: 1,
  },
  hoursItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing(8),
  },
  dayText: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.primary,
    fontWeight: '500',
  },
  hoursText: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
  },
  closedText: {
    color: colors.error,
  },
  categoryFilter: {
    marginBottom: getResponsiveSpacing(16),
  },
  categoryChip: {
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(8),
    borderRadius: getResponsiveSpacing(20),
    backgroundColor: colors.background.secondary,
    marginRight: getResponsiveSpacing(8),
    borderWidth: 1,
    borderColor: colors.border.light,
  },
  categoryChipActive: {
    backgroundColor: colors.sage600,
    borderColor: colors.sage600,
  },
  categoryChipText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
    fontWeight: '500',
  },
  categoryChipTextActive: {
    color: colors.text.onPrimary,
    fontWeight: '600',
  },
  serviceCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: getResponsiveSpacing(16),
    backgroundColor: colors.background.secondary,
    borderRadius: getResponsiveSpacing(12),
    marginBottom: getResponsiveSpacing(12),
    borderWidth: 1,
    borderColor: colors.border.light,
  },
  serviceInfo: {
    flex: 1,
    marginRight: getResponsiveSpacing(12),
  },
  serviceName: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(4),
    flex: 1,
    marginRight: getResponsiveSpacing(12),
  },
  serviceDescription: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
    marginBottom: getResponsiveSpacing(16),
    lineHeight: getResponsiveFontSize(20),
  },
  serviceDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  servicePrice: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '700',
    color: colors.sage700,
    marginRight: getResponsiveSpacing(12),
  },
  serviceDuration: {
    fontSize: getResponsiveFontSize(12),
    color: colors.text.secondary,
    marginTop: getResponsiveSpacing(2),
  },
  bookButton: {
    minWidth: 80,
  },
  // Enhanced service card styles
  enhancedServiceCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 16,
    padding: getResponsiveSpacing(20),
    marginBottom: getResponsiveSpacing(16),
    borderWidth: 1,
    borderColor: colors.border.light,
    shadowColor: colors.shadow?.color || '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  serviceContent: {
    flex: 1,
  },
  serviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: getResponsiveSpacing(8),
  },
  servicePriceContainer: {
    alignItems: 'flex-end',
  },
  serviceActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: getResponsiveSpacing(12),
  },
  enhancedBookButton: {
    paddingHorizontal: getResponsiveSpacing(32),
    paddingVertical: getResponsiveSpacing(12),
    borderRadius: 25,
    minWidth: 120,
  },
  // Compact service card styles
  compactServiceCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: getResponsiveSpacing(16),
    marginBottom: getResponsiveSpacing(8),
    borderWidth: 1,
    borderColor: colors.border.light,
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 72,
    shadowColor: colors.shadow?.color || '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  serviceMainContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  serviceInfo: {
    flex: 1,
    marginRight: getResponsiveSpacing(12),
  },
  compactServiceName: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(4),
  },
  compactServiceDescription: {
    fontSize: getResponsiveFontSize(13),
    color: colors.text.secondary,
    lineHeight: getResponsiveFontSize(18),
  },
  servicePricing: {
    alignItems: 'flex-end',
    minWidth: 60,
  },
  compactServicePrice: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '700',
    color: colors.sage600,
    marginBottom: getResponsiveSpacing(2),
  },
  compactServiceDuration: {
    fontSize: getResponsiveFontSize(12),
    color: colors.text.secondary,
    fontWeight: '500',
  },
  serviceBookingIndicator: {
    marginLeft: getResponsiveSpacing(8),
    padding: getResponsiveSpacing(4),
  },
  portfolioContainer: {
    marginTop: getResponsiveSpacing(8),
  },
  portfolioContent: {
    paddingRight: getResponsiveSpacing(20),
  },
  portfolioImageContainer: {
    marginRight: getResponsiveSpacing(12),
    borderRadius: getResponsiveSpacing(12),
    overflow: 'hidden',
    shadowColor: colors.shadow?.color || '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  portfolioImage: {
    width: 140,
    height: 140,
    backgroundColor: colors.background.secondary,
  },
});
