/**
 * Customer Home Flow Integration Tests
 *
 * Test Coverage:
 * - End-to-end user flows
 * - Service integration
 * - Navigation flows
 * - Error handling flows
 * - Performance under load
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { CustomerHomeScreen } from '../../screens/CustomerHomeScreen';
import { ThemeProvider } from '../../contexts/ThemeContext';
import customerService from '../../services/customerService';
import { performanceMonitor } from '../../services/performanceMonitor';
import cacheService from '../../services/cacheService';

// Mock services
jest.mock('../../services/customerService');
jest.mock('../../services/performanceMonitor');
jest.mock('../../store/authSlice');

const mockCustomerService = customerService as jest.Mocked<typeof customerService>;
const mockPerformanceMonitor = performanceMonitor as jest.Mocked<typeof performanceMonitor>;

// Test wrapper
const IntegrationTestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <NavigationContainer>
    <ThemeProvider>
      {children}
    </ThemeProvider>
  </NavigationContainer>
);

// Mock data
const mockCategories = [
  { id: 1, name: 'Hair', slug: 'hair', icon: 'cut', color: '#FF6B6B' },
  { id: 2, name: 'Nails', slug: 'nails', icon: 'hand', color: '#4ECDC4' },
  { id: 3, name: 'Skincare', slug: 'skincare', icon: 'face', color: '#45B7D1' },
];

const mockProviders = [
  {
    id: 1,
    name: 'Beauty Studio',
    rating: 4.8,
    reviewCount: 150,
    imageUrl: 'https://example.com/provider1.jpg',
    services: ['Hair', 'Makeup'],
    distance: 2.5,
  },
  {
    id: 2,
    name: 'Spa Wellness',
    rating: 4.6,
    reviewCount: 89,
    imageUrl: 'https://example.com/provider2.jpg',
    services: ['Skincare', 'Massage'],
    distance: 1.8,
  },
];

describe('Customer Home Flow Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    cacheService.clear();
    
    // Setup service mocks
    mockCustomerService.getServiceCategories.mockResolvedValue(mockCategories);
    mockCustomerService.getFeaturedProviders.mockResolvedValue(mockProviders);
    mockCustomerService.getNearbyProviders.mockResolvedValue([]);
    mockCustomerService.getFavoriteProviders.mockResolvedValue([]);
    mockCustomerService.getRecentBookings.mockResolvedValue([]);
    mockCustomerService.getCustomerDashboard.mockResolvedValue(null);
    
    // Setup performance monitor
    mockPerformanceMonitor.startMonitoring.mockImplementation();
    mockPerformanceMonitor.trackRender.mockImplementation();
    mockPerformanceMonitor.trackUserInteraction.mockImplementation();
    mockPerformanceMonitor.trackNetworkRequest.mockImplementation();
  });

  describe('Complete User Journey', () => {
    it('loads home screen and displays all data correctly', async () => {
      render(
        <IntegrationTestWrapper>
          <CustomerHomeScreen />
        </IntegrationTestWrapper>
      );

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('Browse Services')).toBeTruthy();
      });

      // Check categories loaded
      await waitFor(() => {
        expect(screen.getByText('Hair')).toBeTruthy();
        expect(screen.getByText('Nails')).toBeTruthy();
        expect(screen.getByText('Skincare')).toBeTruthy();
      });

      // Check providers loaded
      await waitFor(() => {
        expect(screen.getByText('Beauty Studio')).toBeTruthy();
        expect(screen.getByText('Spa Wellness')).toBeTruthy();
      });

      // Verify service calls
      expect(mockCustomerService.getServiceCategories).toHaveBeenCalled();
      expect(mockCustomerService.getFeaturedProviders).toHaveBeenCalled();
    });

    it('handles category selection flow', async () => {
      const mockNavigate = jest.fn();
      
      // Mock navigation
      jest.doMock('../../hooks/useNavigationGuard', () => ({
        useNavigationGuard: () => ({
          navigate: mockNavigate,
          canNavigate: () => true,
        }),
      }));

      render(
        <IntegrationTestWrapper>
          <CustomerHomeScreen />
        </IntegrationTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Hair')).toBeTruthy();
      });

      // Click on Hair category
      fireEvent.press(screen.getByText('Hair'));

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('Search', { category: 'hair' });
      });

      // Verify performance tracking
      expect(mockPerformanceMonitor.trackUserInteraction).toHaveBeenCalledWith(
        'category_press',
        expect.any(Number),
        expect.any(Object)
      );
    });

    it('handles provider selection flow', async () => {
      const mockNavigate = jest.fn();
      
      jest.doMock('../../hooks/useNavigationGuard', () => ({
        useNavigationGuard: () => ({
          navigate: mockNavigate,
          canNavigate: () => true,
        }),
      }));

      render(
        <IntegrationTestWrapper>
          <CustomerHomeScreen />
        </IntegrationTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Beauty Studio')).toBeTruthy();
      });

      // Click on provider
      fireEvent.press(screen.getByText('Beauty Studio'));

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('ProviderDetails', { providerId: 1 });
      });
    });
  });

  describe('Error Handling Integration', () => {
    it('handles service failures gracefully', async () => {
      // Mock service failures
      mockCustomerService.getServiceCategories.mockRejectedValue(
        new Error('Network error')
      );
      mockCustomerService.getFeaturedProviders.mockRejectedValue(
        new Error('Server error')
      );

      render(
        <IntegrationTestWrapper>
          <CustomerHomeScreen />
        </IntegrationTestWrapper>
      );

      // Should still render the screen
      await waitFor(() => {
        expect(screen.getByTestId('customer-home-screen')).toBeTruthy();
      });

      // Should show error states
      await waitFor(() => {
        expect(screen.getByTestId('categories-error')).toBeTruthy();
        expect(screen.getByTestId('featured-providers-error')).toBeTruthy();
      });
    });

    it('handles partial service failures', async () => {
      // Only categories fail
      mockCustomerService.getServiceCategories.mockRejectedValue(
        new Error('Categories failed')
      );

      render(
        <IntegrationTestWrapper>
          <CustomerHomeScreen />
        </IntegrationTestWrapper>
      );

      // Categories should show error
      await waitFor(() => {
        expect(screen.getByTestId('categories-error')).toBeTruthy();
      });

      // Providers should load successfully
      await waitFor(() => {
        expect(screen.getByText('Beauty Studio')).toBeTruthy();
      });
    });

    it('handles retry functionality', async () => {
      let callCount = 0;
      mockCustomerService.getServiceCategories.mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          return Promise.reject(new Error('First call fails'));
        }
        return Promise.resolve(mockCategories);
      });

      render(
        <IntegrationTestWrapper>
          <CustomerHomeScreen />
        </IntegrationTestWrapper>
      );

      // Should show error initially
      await waitFor(() => {
        expect(screen.getByTestId('categories-error')).toBeTruthy();
      });

      // Click retry button
      const retryButton = screen.getByTestId('categories-retry-button');
      fireEvent.press(retryButton);

      // Should load successfully on retry
      await waitFor(() => {
        expect(screen.getByText('Hair')).toBeTruthy();
      });

      expect(callCount).toBe(2);
    });
  });

  describe('Performance Integration', () => {
    it('tracks performance metrics during normal flow', async () => {
      render(
        <IntegrationTestWrapper>
          <CustomerHomeScreen />
        </IntegrationTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Browse Services')).toBeTruthy();
      });

      // Should track component render
      expect(mockPerformanceMonitor.trackRender).toHaveBeenCalledWith(
        'CustomerHomeScreen',
        expect.any(Number),
        expect.any(Object)
      );

      // Should track network requests
      expect(mockPerformanceMonitor.trackNetworkRequest).toHaveBeenCalledWith(
        '/api/v1/catalog/categories/',
        'GET',
        expect.any(Number),
        200,
        0,
        0,
        expect.any(Boolean)
      );
    });

    it('handles performance under load', async () => {
      // Simulate slow network
      mockCustomerService.getServiceCategories.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(mockCategories), 2000))
      );

      const startTime = Date.now();

      render(
        <IntegrationTestWrapper>
          <CustomerHomeScreen />
        </IntegrationTestWrapper>
      );

      // Should show loading state
      expect(screen.getByTestId('categories-loading')).toBeTruthy();

      // Fast-forward time
      act(() => {
        jest.advanceTimersByTime(2000);
      });

      await waitFor(() => {
        expect(screen.getByText('Hair')).toBeTruthy();
      });

      const endTime = Date.now();
      const loadTime = endTime - startTime;

      // Should track slow network request
      expect(mockPerformanceMonitor.trackNetworkRequest).toHaveBeenCalledWith(
        expect.any(String),
        'GET',
        expect.any(Number),
        200,
        0,
        0,
        false
      );
    });
  });

  describe('Cache Integration', () => {
    it('uses cached data when available', async () => {
      // Pre-populate cache
      await cacheService.set('customer_home_categories', mockCategories);
      await cacheService.set('customer_home_featured_providers', mockProviders);

      render(
        <IntegrationTestWrapper>
          <CustomerHomeScreen />
        </IntegrationTestWrapper>
      );

      // Should load immediately from cache
      await waitFor(() => {
        expect(screen.getByText('Hair')).toBeTruthy();
        expect(screen.getByText('Beauty Studio')).toBeTruthy();
      });

      // Should track cache hits
      expect(mockPerformanceMonitor.trackNetworkRequest).toHaveBeenCalledWith(
        expect.any(String),
        'GET',
        expect.any(Number),
        200,
        0,
        0,
        true // cached
      );
    });

    it('falls back to API when cache is expired', async () => {
      // Set expired cache entry
      await cacheService.set('customer_home_categories', mockCategories, 1); // 1ms TTL
      
      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 10));

      render(
        <IntegrationTestWrapper>
          <CustomerHomeScreen />
        </IntegrationTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Hair')).toBeTruthy();
      });

      // Should call API since cache expired
      expect(mockCustomerService.getServiceCategories).toHaveBeenCalled();
    });

    it('updates cache after successful API calls', async () => {
      render(
        <IntegrationTestWrapper>
          <CustomerHomeScreen />
        </IntegrationTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Hair')).toBeTruthy();
      });

      // Check that data was cached
      const cachedCategories = await cacheService.get('customer_home_categories');
      expect(cachedCategories).toEqual(mockCategories);

      const cachedProviders = await cacheService.get('customer_home_featured_providers');
      expect(cachedProviders).toEqual(mockProviders);
    });
  });

  describe('Refresh Integration', () => {
    it('handles pull-to-refresh correctly', async () => {
      render(
        <IntegrationTestWrapper>
          <CustomerHomeScreen />
        </IntegrationTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Hair')).toBeTruthy();
      });

      // Clear mock call history
      jest.clearAllMocks();

      // Trigger refresh
      const scrollView = screen.getByTestId('home-scroll-view');
      fireEvent(scrollView, 'refresh');

      // Should call services again
      await waitFor(() => {
        expect(mockCustomerService.getServiceCategories).toHaveBeenCalled();
        expect(mockCustomerService.getFeaturedProviders).toHaveBeenCalled();
      });
    });

    it('shows refreshing state during refresh', async () => {
      // Mock slow refresh
      mockCustomerService.getServiceCategories.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(mockCategories), 1000))
      );

      render(
        <IntegrationTestWrapper>
          <CustomerHomeScreen />
        </IntegrationTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Hair')).toBeTruthy();
      });

      // Trigger refresh
      const scrollView = screen.getByTestId('home-scroll-view');
      fireEvent(scrollView, 'refresh');

      // Should show refreshing state
      expect(scrollView.props.refreshControl.props.refreshing).toBe(true);

      // Fast-forward time
      act(() => {
        jest.advanceTimersByTime(1000);
      });

      await waitFor(() => {
        expect(scrollView.props.refreshControl.props.refreshing).toBe(false);
      });
    });
  });

  describe('Memory and Resource Management', () => {
    it('cleans up resources on unmount', () => {
      const { unmount } = render(
        <IntegrationTestWrapper>
          <CustomerHomeScreen />
        </IntegrationTestWrapper>
      );

      // Unmount component
      unmount();

      // Should clean up performance monitoring
      expect(mockPerformanceMonitor.trackRender).toHaveBeenCalledWith(
        'CustomerHomeScreen',
        expect.any(Number),
        expect.objectContaining({
          type: 'unmount',
        })
      );
    });

    it('handles multiple rapid re-renders efficiently', async () => {
      const { rerender } = render(
        <IntegrationTestWrapper>
          <CustomerHomeScreen />
        </IntegrationTestWrapper>
      );

      // Trigger multiple re-renders
      for (let i = 0; i < 10; i++) {
        rerender(
          <IntegrationTestWrapper>
            <CustomerHomeScreen />
          </IntegrationTestWrapper>
        );
      }

      await waitFor(() => {
        expect(screen.getByText('Hair')).toBeTruthy();
      });

      // Should not cause memory leaks or excessive API calls
      expect(mockCustomerService.getServiceCategories).toHaveBeenCalledTimes(1);
    });
  });
});
