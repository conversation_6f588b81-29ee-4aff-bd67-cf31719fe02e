/**
 * CustomerHomeScreen Tests - Comprehensive test suite
 *
 * Test Coverage:
 * - Component rendering and UI elements
 * - User interactions and navigation
 * - Data loading and error states
 * - Performance and accessibility
 * - Integration with services and hooks
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { CustomerHomeScreen } from '../CustomerHomeScreen';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { useCustomerHomeData } from '../../hooks/useCustomerHomeData';
import { useAuthStore } from '../../store/authSlice';
import { useNavigationGuard } from '../../hooks/useNavigationGuard';
import { performanceMonitor } from '../../services/performanceMonitor';

// Mock dependencies
jest.mock('../../hooks/useCustomerHomeData');
jest.mock('../../store/authSlice');
jest.mock('../../hooks/useNavigationGuard');
jest.mock('../../services/performanceMonitor');
jest.mock('../../hooks/usePerformance');
jest.mock('../../hooks/useErrorHandling');

const mockUseCustomerHomeData = useCustomerHomeData as jest.MockedFunction<typeof useCustomerHomeData>;
const mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>;
const mockUseNavigationGuard = useNavigationGuard as jest.MockedFunction<typeof useNavigationGuard>;

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <NavigationContainer>
    <ThemeProvider>
      {children}
    </ThemeProvider>
  </NavigationContainer>
);

// Mock data
const mockCategories = [
  { id: 1, name: 'Hair', slug: 'hair', icon: 'cut', color: '#FF6B6B' },
  { id: 2, name: 'Nails', slug: 'nails', icon: 'hand', color: '#4ECDC4' },
  { id: 3, name: 'Skincare', slug: 'skincare', icon: 'face', color: '#45B7D1' },
];

const mockFeaturedProviders = [
  {
    id: 1,
    name: 'Beauty Studio',
    rating: 4.8,
    reviewCount: 150,
    imageUrl: 'https://example.com/provider1.jpg',
    services: ['Hair', 'Makeup'],
    distance: 2.5,
  },
  {
    id: 2,
    name: 'Spa Wellness',
    rating: 4.6,
    reviewCount: 89,
    imageUrl: 'https://example.com/provider2.jpg',
    services: ['Skincare', 'Massage'],
    distance: 1.8,
  },
];

const mockUser = {
  id: 1,
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  role: 'customer',
};

describe('CustomerHomeScreen', () => {
  const mockNavigate = jest.fn();
  const mockRefresh = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    mockUseAuthStore.mockReturnValue({
      isAuthenticated: true,
      user: mockUser,
      login: jest.fn(),
      logout: jest.fn(),
      register: jest.fn(),
    });

    mockUseNavigationGuard.mockReturnValue({
      navigate: mockNavigate,
      canNavigate: jest.fn(() => true),
      guardedNavigate: mockNavigate,
    });

    mockUseCustomerHomeData.mockReturnValue({
      data: {
        categories: mockCategories,
        featuredProviders: mockFeaturedProviders,
        nearbyProviders: [],
        favoriteProviders: [],
        recentBookings: [],
        dashboard: null,
      },
      loading: {
        categories: false,
        featuredProviders: false,
        nearbyProviders: false,
        favoriteProviders: false,
        recentBookings: false,
        dashboard: false,
      },
      error: {
        categories: null,
        featuredProviders: null,
        nearbyProviders: null,
        favoriteProviders: null,
        recentBookings: null,
        dashboard: null,
      },
      refreshing: false,
      refresh: mockRefresh,
    });
  });

  describe('Rendering', () => {
    it('renders the home screen correctly', async () => {
      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      // Check for main elements
      expect(screen.getByTestId('customer-home-screen')).toBeTruthy();
      expect(screen.getByText('Browse Services')).toBeTruthy();
      expect(screen.getByText('Featured Providers')).toBeTruthy();
    });

    it('displays user greeting when authenticated', async () => {
      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/Hello, Test/)).toBeTruthy();
      });
    });

    it('renders categories correctly', async () => {
      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Hair')).toBeTruthy();
        expect(screen.getByText('Nails')).toBeTruthy();
        expect(screen.getByText('Skincare')).toBeTruthy();
      });
    });

    it('renders featured providers correctly', async () => {
      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Beauty Studio')).toBeTruthy();
        expect(screen.getByText('Spa Wellness')).toBeTruthy();
        expect(screen.getByText('4.8')).toBeTruthy();
        expect(screen.getByText('4.6')).toBeTruthy();
      });
    });
  });

  describe('Loading States', () => {
    it('shows loading indicators when data is loading', async () => {
      mockUseCustomerHomeData.mockReturnValue({
        data: {
          categories: [],
          featuredProviders: [],
          nearbyProviders: [],
          favoriteProviders: [],
          recentBookings: [],
          dashboard: null,
        },
        loading: {
          categories: true,
          featuredProviders: true,
          nearbyProviders: false,
          favoriteProviders: false,
          recentBookings: false,
          dashboard: false,
        },
        error: {
          categories: null,
          featuredProviders: null,
          nearbyProviders: null,
          favoriteProviders: null,
          recentBookings: null,
          dashboard: null,
        },
        refreshing: false,
        refresh: mockRefresh,
      });

      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      expect(screen.getByTestId('categories-loading')).toBeTruthy();
      expect(screen.getByTestId('featured-providers-loading')).toBeTruthy();
    });

    it('shows refreshing state correctly', async () => {
      mockUseCustomerHomeData.mockReturnValue({
        data: {
          categories: mockCategories,
          featuredProviders: mockFeaturedProviders,
          nearbyProviders: [],
          favoriteProviders: [],
          recentBookings: [],
          dashboard: null,
        },
        loading: {
          categories: false,
          featuredProviders: false,
          nearbyProviders: false,
          favoriteProviders: false,
          recentBookings: false,
          dashboard: false,
        },
        error: {
          categories: null,
          featuredProviders: null,
          nearbyProviders: null,
          favoriteProviders: null,
          recentBookings: null,
          dashboard: null,
        },
        refreshing: true,
        refresh: mockRefresh,
      });

      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      // The RefreshControl should be active
      const scrollView = screen.getByTestId('home-scroll-view');
      expect(scrollView.props.refreshControl.props.refreshing).toBe(true);
    });
  });

  describe('Error States', () => {
    it('handles category loading errors gracefully', async () => {
      mockUseCustomerHomeData.mockReturnValue({
        data: {
          categories: [],
          featuredProviders: mockFeaturedProviders,
          nearbyProviders: [],
          favoriteProviders: [],
          recentBookings: [],
          dashboard: null,
        },
        loading: {
          categories: false,
          featuredProviders: false,
          nearbyProviders: false,
          favoriteProviders: false,
          recentBookings: false,
          dashboard: false,
        },
        error: {
          categories: new Error('Failed to load categories'),
          featuredProviders: null,
          nearbyProviders: null,
          favoriteProviders: null,
          recentBookings: null,
          dashboard: null,
        },
        refreshing: false,
        refresh: mockRefresh,
      });

      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('categories-error')).toBeTruthy();
      });
    });

    it('handles provider loading errors gracefully', async () => {
      mockUseCustomerHomeData.mockReturnValue({
        data: {
          categories: mockCategories,
          featuredProviders: [],
          nearbyProviders: [],
          favoriteProviders: [],
          recentBookings: [],
          dashboard: null,
        },
        loading: {
          categories: false,
          featuredProviders: false,
          nearbyProviders: false,
          favoriteProviders: false,
          recentBookings: false,
          dashboard: false,
        },
        error: {
          categories: null,
          featuredProviders: new Error('Failed to load providers'),
          nearbyProviders: null,
          favoriteProviders: null,
          recentBookings: null,
          dashboard: null,
        },
        refreshing: false,
        refresh: mockRefresh,
      });

      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('featured-providers-error')).toBeTruthy();
      });
    });
  });

  describe('User Interactions', () => {
    it('handles category press correctly', async () => {
      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        const hairCategory = screen.getByText('Hair');
        fireEvent.press(hairCategory);
      });

      expect(mockNavigate).toHaveBeenCalledWith('Search', { category: 'hair' });
    });

    it('handles provider press correctly', async () => {
      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        const provider = screen.getByText('Beauty Studio');
        fireEvent.press(provider);
      });

      expect(mockNavigate).toHaveBeenCalledWith('ProviderDetails', { providerId: 1 });
    });

    it('handles pull to refresh correctly', async () => {
      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      const scrollView = screen.getByTestId('home-scroll-view');
      
      await act(async () => {
        fireEvent(scrollView, 'refresh');
      });

      expect(mockRefresh).toHaveBeenCalled();
    });

    it('handles see all buttons correctly', async () => {
      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        const seeAllButton = screen.getByTestId('featured-providers-see-all');
        fireEvent.press(seeAllButton);
      });

      expect(mockNavigate).toHaveBeenCalledWith('Search', { filter: 'featured' });
    });
  });

  describe('Performance', () => {
    it('tracks component render performance', async () => {
      const performanceSpy = jest.spyOn(performanceMonitor, 'trackRender');

      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(performanceSpy).toHaveBeenCalledWith(
          'CustomerHomeScreen',
          expect.any(Number),
          expect.any(Object)
        );
      });
    });

    it('tracks user interaction performance', async () => {
      const interactionSpy = jest.spyOn(performanceMonitor, 'trackUserInteraction');

      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        const hairCategory = screen.getByText('Hair');
        fireEvent.press(hairCategory);
      });

      expect(interactionSpy).toHaveBeenCalledWith(
        'category_press',
        expect.any(Number),
        expect.any(Object)
      );
    });

    it('renders within performance threshold', async () => {
      const renderTime = await measureAsyncPerformance('CustomerHomeScreen render', async () => {
        render(
          <TestWrapper>
            <CustomerHomeScreen />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByTestId('customer-home-screen')).toBeTruthy();
        });
      });

      // Should render within 1000ms
      expect(renderTime).toBeLessThan(1000);
    });
  });

  describe('Accessibility', () => {
    it('has proper accessibility labels', async () => {
      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      expect(screen.getByLabelText('Customer Home Screen')).toBeTruthy();
      expect(screen.getByLabelText('Main content area')).toBeTruthy();
      expect(screen.getByLabelText('Pull to refresh')).toBeTruthy();
    });

    it('has proper accessibility roles', async () => {
      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      const scrollView = screen.getByTestId('home-scroll-view');
      expect(scrollView.props.accessibilityRole).toBe('scrollbar');
    });

    it('supports screen reader navigation', async () => {
      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        const categories = screen.getAllByRole('button');
        expect(categories.length).toBeGreaterThan(0);
        
        categories.forEach(category => {
          expect(category.props.accessibilityLabel).toBeTruthy();
        });
      });
    });
  });

  describe('Integration', () => {
    it('integrates with auth store correctly', async () => {
      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      expect(mockUseAuthStore).toHaveBeenCalled();
    });

    it('integrates with navigation guard correctly', async () => {
      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      expect(mockUseNavigationGuard).toHaveBeenCalled();
    });

    it('integrates with customer home data hook correctly', async () => {
      render(
        <TestWrapper>
          <CustomerHomeScreen />
        </TestWrapper>
      );

      expect(mockUseCustomerHomeData).toHaveBeenCalled();
    });
  });
});
