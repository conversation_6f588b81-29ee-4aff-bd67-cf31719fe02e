/**
 * Booking Service - API Integration for Booking Management
 *
 * Component Contract:
 * - Handles all booking-related API calls
 * - Provides booking creation, retrieval, and management
 * - Integrates with backend booking endpoints
 * - Supports time slot availability checking
 * - Handles booking status updates and cancellations
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { apiClient } from './apiClient';

// Types
export type BookingStatus =
  | 'pending'
  | 'confirmed'
  | 'in_progress'
  | 'completed'
  | 'cancelled'
  | 'no_show';

export type PaymentStatus =
  | 'pending'
  | 'processing'
  | 'paid'
  | 'failed'
  | 'refunded'
  | 'partially_refunded';

export interface Booking {
  id: string;
  bookingNumber: string;
  service: {
    id: string;
    name: string;
    price: number;
    duration: number;
    category?: string;
  };
  provider: {
    id: string;
    name: string;
    image?: string;
    address?: string;
    phone?: string;
    email?: string;
  };
  customer: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  };
  date: string;
  time: string;
  endTime?: string;
  status: BookingStatus;
  payment: {
    status: PaymentStatus;
    method?: string;
    amount: number;
    transactionId?: string;
    receiptUrl?: string;
  };
  notes?: string;
  specialRequests?: string;
  cancellationReason?: string;
  cancellationPolicy?: string;
  refundAmount?: number;
  created_at: string;
  updated_at: string;
}

export interface TimeSlot {
  time: string;
  available: boolean;
  provider_id: string;
  date: string;
  price?: number;
  duration?: number;
}

export interface BookingFilters {
  status?: BookingStatus;
  dateFrom?: string;
  dateTo?: string;
  providerId?: string;
  serviceCategory?: string;
  paymentStatus?: PaymentStatus;
}
export interface BookingData {
  serviceId: string;
  providerId: string;
  date: string;
  timeSlot: string;
  notes?: string;
  customerInfo?: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  paymentMethod?: string;
  specialRequests?: string;
}

// Mock data for fallback
const MOCK_BOOKINGS: Booking[] = [
  {
    id: 'booking_1',
    customer_id: 'customer_1',
    provider_id: 'provider_1',
    provider_name: 'Bella Beauty Studio',
    service_id: 'service_1',
    service_name: 'Hair Cut & Style',
    service_category: 'Hair',
    scheduled_datetime: '2024-01-20T14:00:00Z',
    duration_minutes: 60,
    base_price: 45.00,
    total_amount: 45.00,
    status: 'confirmed',
    payment_status: 'paid',
    notes: 'Regular trim and style',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
  },
  {
    id: 'booking_2',
    customer_id: 'customer_1',
    provider_id: 'provider_2',
    provider_name: 'Glow Spa & Wellness',
    service_id: 'service_2',
    service_name: 'Facial Treatment',
    service_category: 'Skincare',
    scheduled_datetime: '2024-01-10T11:00:00Z',
    duration_minutes: 90,
    base_price: 75.00,
    total_amount: 75.00,
    status: 'completed',
    payment_status: 'paid',
    notes: 'Deep cleansing facial',
    created_at: '2024-01-08T14:30:00Z',
    updated_at: '2024-01-10T12:30:00Z',
  },
];

const MOCK_TIME_SLOTS: TimeSlot[] = [
  { time: '09:00', available: true, provider_id: 'provider_1', date: '2024-01-20' },
  { time: '09:30', available: true, provider_id: 'provider_1', date: '2024-01-20' },
  { time: '10:00', available: false, provider_id: 'provider_1', date: '2024-01-20' },
  { time: '10:30', available: true, provider_id: 'provider_1', date: '2024-01-20' },
  { time: '11:00', available: true, provider_id: 'provider_1', date: '2024-01-20' },
  { time: '11:30', available: false, provider_id: 'provider_1', date: '2024-01-20' },
  { time: '14:00', available: true, provider_id: 'provider_1', date: '2024-01-20' },
  { time: '14:30', available: true, provider_id: 'provider_1', date: '2024-01-20' },
  { time: '15:00', available: true, provider_id: 'provider_1', date: '2024-01-20' },
  { time: '15:30', available: false, provider_id: 'provider_1', date: '2024-01-20' },
  { time: '16:00', available: true, provider_id: 'provider_1', date: '2024-01-20' },
  { time: '16:30', available: true, provider_id: 'provider_1', date: '2024-01-20' },
];

export interface Booking {
  id: string;
  booking_number: string;
  customer_id: string;
  provider_id: string;
  provider_name: string;
  service_id: string;
  service_name: string;
  service_category: string;
  scheduled_datetime: string;
  duration_minutes: number;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';
  payment_status: 'pending' | 'paid' | 'partially_paid' | 'refunded' | 'failed';
  total_amount: number;
  base_price: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface TimeSlot {
  id: string;
  time: string;
  available: boolean;
  provider_id: string;
  date: string;
}

export interface BookingResponse {
  results: Booking[];
  count: number;
  next?: string;
  previous?: string;
}

export interface BookingFilters {
  status?: string;
  date_from?: string;
  date_to?: string;
  provider?: string;
  service?: string;
}

class BookingService {
  private readonly baseUrl = '/api/bookings';

  /**
   * Get bookings with optional filters
   */
  async getBookings(filters?: BookingFilters): Promise<BookingResponse> {
    try {
      const params = new URLSearchParams();
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value) {
            params.append(key, value);
          }
        });
      }

      const url = `${this.baseUrl}/${params.toString() ? `?${params.toString()}` : ''}`;
      const response = await apiClient.get<BookingResponse>(url);
      return response.data;
    } catch (error: any) {
      // Enhanced error handling for different types of errors
      let errorMessage = 'Error fetching bookings';

      if (error?.status === 401) {
        errorMessage = 'Authentication error - token may be invalid or expired';
        console.warn('🔐 Bookings API: Authentication failed, using fallback data');
      } else if (error?.status === 403) {
        errorMessage = 'Access denied - insufficient permissions';
        console.warn('🚫 Bookings API: Access denied, using fallback data');
      } else if (error?.status === 404) {
        errorMessage = 'Bookings endpoint not found';
        console.warn('🔍 Bookings API: Endpoint not found, using fallback data');
      } else if (error?.status === 500) {
        errorMessage = 'Server error occurred';
        console.warn('🔥 Bookings API: Server error, using fallback data');
      } else if (error?.status === 0 || !error?.status) {
        errorMessage = 'Network connection error';
        console.warn('🌐 Bookings API: Network error, using fallback data');
      } else {
        errorMessage = `API error (${error?.status || 'unknown'})`;
        console.warn(`⚠️ Bookings API: ${errorMessage}, using fallback data`);
      }

      console.error('Bookings API Error Details:', {
        status: error?.status,
        message: error?.message,
        details: error?.details,
        fallbackUsed: true
      });

      // Return mock data as fallback
      let filteredBookings = MOCK_BOOKINGS;

      if (filters?.status) {
        filteredBookings = filteredBookings.filter(booking => booking.status === filters.status);
      }

      return {
        count: filteredBookings.length,
        next: null,
        previous: null,
        results: filteredBookings,
      };
    }
  }

  /**
   * Get upcoming bookings
   */
  async getUpcomingBookings(): Promise<BookingResponse> {
    try {
      const response = await apiClient.get<BookingResponse>(`${this.baseUrl}/upcoming/`);
      return response.data;
    } catch (error: any) {
      // Enhanced error handling for upcoming bookings
      if (error?.status === 401) {
        console.warn('🔐 Upcoming Bookings API: Authentication failed');
      } else if (error?.status === 403) {
        console.warn('🚫 Upcoming Bookings API: Access denied');
      } else if (error?.status === 0 || !error?.status) {
        console.warn('🌐 Upcoming Bookings API: Network error');
      } else {
        console.warn(`⚠️ Upcoming Bookings API: Error (${error?.status || 'unknown'})`);
      }

      console.error('Upcoming Bookings API Error Details:', {
        status: error?.status,
        message: error?.message,
        details: error?.details
      });

      throw error;
    }
  }

  /**
   * Get booking details by ID
   */
  async getBookingDetails(bookingId: string): Promise<Booking> {
    try {
      const response = await apiClient.get<Booking>(`${this.baseUrl}/${bookingId}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching booking details:', error);
      throw error;
    }
  }

  /**
   * Create a new booking
   */
  async createBooking(bookingData: BookingData): Promise<Booking> {
    try {
      const response = await apiClient.post<Booking>(`${this.baseUrl}/`, {
        service: bookingData.serviceId,
        provider: bookingData.providerId,
        scheduled_datetime: `${bookingData.date}T${bookingData.timeSlot}:00`,
        notes: bookingData.notes || '',
      });
      return response.data;
    } catch (error) {
      console.error('Error creating booking, using mock creation:', error);

      // Create a mock booking as fallback
      const mockBooking: Booking = {
        id: `booking_${Date.now()}`,
        customer_id: 'customer_1',
        provider_id: bookingData.providerId,
        provider_name: 'Mock Provider',
        service_id: bookingData.serviceId,
        service_name: 'Mock Service',
        service_category: 'Beauty',
        scheduled_datetime: `${bookingData.date}T${bookingData.timeSlot}:00Z`,
        duration_minutes: 60,
        base_price: 50.00,
        total_amount: 50.00,
        status: 'pending',
        payment_status: 'pending',
        notes: bookingData.notes || '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Add to mock data for future retrieval
      MOCK_BOOKINGS.push(mockBooking);

      return mockBooking;
    }
  }

  /**
   * Update booking status
   */
  async updateBookingStatus(bookingId: string, status: string): Promise<Booking> {
    try {
      const response = await apiClient.patch<Booking>(`${this.baseUrl}/${bookingId}/`, {
        status,
      });
      return response.data;
    } catch (error) {
      console.error('Error updating booking status:', error);
      throw error;
    }
  }

  /**
   * Cancel a booking
   */
  async cancelBooking(bookingId: string, reason?: string): Promise<Booking> {
    try {
      const response = await apiClient.post<Booking>(`${this.baseUrl}/${bookingId}/cancel/`, {
        reason: reason || 'Customer cancellation',
      });
      return response.data;
    } catch (error) {
      console.error('Error cancelling booking:', error);
      throw error;
    }
  }

  /**
   * Confirm a booking
   */
  async confirmBooking(bookingId: string): Promise<Booking> {
    try {
      const response = await apiClient.post<Booking>(`${this.baseUrl}/${bookingId}/confirm/`);
      return response.data;
    } catch (error) {
      console.error('Error confirming booking:', error);
      throw error;
    }
  }

  /**
   * Get available time slots for a provider and date
   */
  async getAvailableTimeSlots(providerId: string, date: string): Promise<TimeSlot[]> {
    try {
      const response = await apiClient.get<TimeSlot[]>(`/api/bookings/time-slots/`, {
        params: {
          provider: providerId,
          date: date,
          available: true,
        },
      });
      return response.data;
    } catch (error: any) {
      // Enhanced error handling for time slots
      let errorMessage = 'Error fetching time slots';

      if (error?.status === 401) {
        errorMessage = 'Authentication error - token may be invalid or expired';
        console.warn('🔐 Time Slots API: Authentication failed, using fallback data');
      } else if (error?.status === 403) {
        errorMessage = 'Access denied - insufficient permissions';
        console.warn('🚫 Time Slots API: Access denied, using fallback data');
      } else if (error?.status === 404) {
        errorMessage = 'Time slots endpoint not found';
        console.warn('🔍 Time Slots API: Endpoint not found, using fallback data');
      } else if (error?.status === 0 || !error?.status) {
        errorMessage = 'Network connection error';
        console.warn('🌐 Time Slots API: Network error, using fallback data');
      } else {
        errorMessage = `API error (${error?.status || 'unknown'})`;
        console.warn(`⚠️ Time Slots API: ${errorMessage}, using fallback data`);
      }

      console.error('Time Slots API Error Details:', {
        status: error?.status,
        message: error?.message,
        details: error?.details,
        providerId,
        date,
        fallbackUsed: true
      });

      // Return mock time slots as fallback
      return MOCK_TIME_SLOTS.filter(slot =>
        slot.provider_id === providerId &&
        slot.date === date &&
        slot.available
      );
    }
  }

  /**
   * Get booking statistics
   */
  async getBookingStats(): Promise<any> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/bookings/stats/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching booking stats:', error);
      throw error;
    }
  }

  /**
   * Get bookings with filters
   */
  async getBookings(filters?: BookingFilters): Promise<Booking[]> {
    try {
      const params = filters ? { ...filters } : {};
      const response = await apiClient.get<Booking[]>(`${this.baseUrl}/bookings/`, { params });
      return response.data;
    } catch (error) {
      console.error('Failed to get bookings:', error);
      throw new Error('Failed to get bookings');
    }
  }

  /**
   * Get booking by ID
   */
  async getBookingById(bookingId: string): Promise<Booking> {
    try {
      const response = await apiClient.get<Booking>(`${this.baseUrl}/bookings/${bookingId}/`);
      return response.data;
    } catch (error) {
      console.error('Failed to get booking:', error);
      throw new Error('Failed to get booking');
    }
  }

  /**
   * Update booking
   */
  async updateBooking(bookingId: string, updates: Partial<BookingData>): Promise<Booking> {
    try {
      const response = await apiClient.patch<Booking>(`${this.baseUrl}/bookings/${bookingId}/`, updates);
      return response.data;
    } catch (error) {
      console.error('Failed to update booking:', error);
      throw new Error('Failed to update booking');
    }
  }

  /**
   * Cancel booking
   */
  async cancelBooking(bookingId: string, reason?: string): Promise<Booking> {
    try {
      const response = await apiClient.post<Booking>(`${this.baseUrl}/bookings/${bookingId}/cancel/`, {
        reason,
      });
      return response.data;
    } catch (error) {
      console.error('Failed to cancel booking:', error);
      throw new Error('Failed to cancel booking');
    }
  }

  /**
   * Reschedule booking
   */
  async rescheduleBooking(
    bookingId: string,
    newDate: string,
    newTimeSlot: string
  ): Promise<Booking> {
    try {
      const response = await apiClient.post<Booking>(`${this.baseUrl}/bookings/${bookingId}/reschedule/`, {
        date: newDate,
        timeSlot: newTimeSlot,
      });
      return response.data;
    } catch (error) {
      console.error('Failed to reschedule booking:', error);
      throw new Error('Failed to reschedule booking');
    }
  }

  /**
   * Get recent bookings for home screen
   */
  async getRecentBookings(limit: number = 5): Promise<Booking[]> {
    try {
      const response = await apiClient.get<{ results: Booking[] }>(
        `${this.baseUrl}/bookings/recent/`,
        { limit }
      );
      return response.data.results;
    } catch (error) {
      console.error('Failed to fetch recent bookings:', error);
      return [];
    }
  }

  /**
   * Get upcoming bookings
   */
  async getUpcomingBookings(limit: number = 10): Promise<Booking[]> {
    try {
      const response = await apiClient.get<{ results: Booking[] }>(
        `${this.baseUrl}/bookings/upcoming/`,
        { limit }
      );
      return response.data.results;
    } catch (error) {
      console.error('Failed to fetch upcoming bookings:', error);
      return [];
    }
  }
}

export const bookingService = new BookingService();
export default bookingService;
