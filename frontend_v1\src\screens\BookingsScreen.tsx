/**
 * Bookings Screen - Enhanced Customer Booking Management
 *
 * Component Contract:
 * - Displays customer's booking history with advanced filtering
 * - Allows comprehensive booking management (cancel, reschedule, contact provider)
 * - Shows upcoming and past bookings with real-time status updates
 * - Integrates with backend booking API with proper error handling
 * - Supports real-time booking updates via WebSocket
 * - Follows responsive design and accessibility guidelines
 * - Implements proper loading states and offline support
 *
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { View, Text, TouchableOpacity, Modal, Dimensions } from 'react-native';
import { StyleSheet, FlatList, ActivityIndicator, RefreshControl, Alert, ScrollView } from 'react-native';

import { Box } from '../components/atoms/Box';
import { Button } from '../components/atoms/Button';
import { Card } from '../components/atoms/Card';
import { StoreImage } from '../components/molecules/StoreImage';
import { SafeAreaScreen } from '../components/ui/SafeAreaWrapper';
import { HeaderHelpButton } from '../components/help';
// import { BookingCard } from '../components/bookings/BookingCard';
// import { EmptyState } from '../components/ui/EmptyState';
// import { LoadingSpinner } from '../components/ui/LoadingSpinner';
// import { ErrorBoundary } from '../components/ui/ErrorBoundary';
// MegaMenu removed - REC-RESP-001: Using thumb-friendly bottom navigation only
import { useTheme } from '../contexts/ThemeContext';
import { useI18n } from '../contexts/I18nContext';
import { useAuthStore } from '../store/authSlice';
import type { Booking } from '../features/booking/types';
import type { CustomerStackParamList } from '../navigation/types';
import { useBookingsStore } from '../store/bookingsSlice';
// import { bookingService, type BookingStatus } from '../services/bookingService';
// import { enhancedWebSocketService } from '../services/enhancedWebSocketService';
// import { cacheService } from '../services/cacheService';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
  getMinimumTouchTarget,
} from '../utils/responsiveUtils';
import { formatDate, formatTime, formatCurrency } from '../utils/canadianFormats';
// import { showToast } from '../utils/toastUtils';

interface FilterTab {
  id: string;
  name: string;
  count?: number;
  icon?: string;
}

interface BookingAction {
  id: string;
  label: string;
  icon: string;
  color: string;
  action: (booking: Booking) => void;
}

interface BookingModalData {
  visible: boolean;
  booking: Booking | null;
  type: 'details' | 'cancel' | 'reschedule' | 'contact';
}

type BookingsScreenNavigationProp = StackNavigationProp<CustomerStackParamList>;

const { width: screenWidth } = Dimensions.get('window');

// Simple toast utility
const showToast = (message: string, type: 'success' | 'error' | 'info' | 'warning' = 'info') => {
  Alert.alert(
    type === 'error' ? 'Error' : type === 'success' ? 'Success' : 'Info',
    message,
    [{ text: 'OK' }]
  );
};

export const BookingsScreen: React.FC = () => {
  const { colors } = useTheme();
  const { t } = useI18n();
  const { user } = useAuthStore();
  const styles = createStyles(colors);
  const navigation = useNavigation<BookingsScreenNavigationProp>();

  // Enhanced state management
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [refreshing, setRefreshing] = useState(false);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'status' | 'provider'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [modalData, setModalData] = useState<BookingModalData>({
    visible: false,
    booking: null,
    type: 'details'
  });

  // Real-time updates
  const [wsConnected, setWsConnected] = useState(false);

  const {
    fetchBookings: storeFetchBookings,
    cancelBooking: storeCancelBooking,
    getBookingsByStatus,
  } = useBookingsStore();

  // Enhanced filter tabs with counts and icons
  const filterTabs: FilterTab[] = useMemo(() => [
    {
      id: 'all',
      name: t('bookings.filters.all', 'All'),
      icon: 'list-outline',
      count: bookings.length
    },
    {
      id: 'upcoming',
      name: t('bookings.filters.upcoming', 'Upcoming'),
      icon: 'calendar-outline',
      count: bookings.filter(b => ['pending', 'confirmed'].includes(b.status)).length
    },
    {
      id: 'in_progress',
      name: t('bookings.filters.inProgress', 'In Progress'),
      icon: 'play-circle-outline',
      count: bookings.filter(b => b.status === 'in_progress').length
    },
    {
      id: 'completed',
      name: t('bookings.filters.completed', 'Completed'),
      icon: 'checkmark-circle-outline',
      count: bookings.filter(b => b.status === 'completed').length
    },
    {
      id: 'cancelled',
      name: t('bookings.filters.cancelled', 'Cancelled'),
      icon: 'close-circle-outline',
      count: bookings.filter(b => b.status === 'cancelled').length
    },
  ], [bookings, t]);

  // Enhanced data loading
  useEffect(() => {
    loadBookings();
  }, []);

  useEffect(() => {
    loadBookings();
  }, [selectedFilter, sortBy, sortOrder]);

  const loadBookings = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use existing store methods
      if (selectedFilter === 'all') {
        await storeFetchBookings();
      } else {
        await getBookingsByStatus(selectedFilter);
      }

      // Get bookings from store and apply sorting
      const storeBookings = useBookingsStore.getState().bookings;
      const sortedBookings = sortBookings(storeBookings);
      setBookings(sortedBookings);

    } catch (error) {
      console.error('Failed to load bookings:', error);
      setError(error instanceof Error ? error.message : 'Failed to load bookings');
      showToast('Failed to load bookings', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Enhanced sorting function
  const sortBookings = useCallback((bookingsToSort: Booking[]) => {
    return [...bookingsToSort].sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'date':
          comparison = new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime();
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
        case 'provider':
          comparison = a.providerName.localeCompare(b.providerName);
          break;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });
  }, [sortBy, sortOrder]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadBookings();
    setRefreshing(false);
  };

  // Enhanced booking actions
  const getBookingActions = useCallback((booking: Booking): BookingAction[] => {
    const actions: BookingAction[] = [];

    // View details action (always available)
    actions.push({
      id: 'details',
      label: t('bookings.actions.viewDetails', 'View Details'),
      icon: 'information-circle-outline',
      color: colors.primary,
      action: (booking) => setModalData({ visible: true, booking, type: 'details' })
    });

    // Contact provider action
    actions.push({
      id: 'contact',
      label: t('bookings.actions.contactProvider', 'Contact Provider'),
      icon: 'chatbubble-outline',
      color: colors.success,
      action: (booking) => setModalData({ visible: true, booking, type: 'contact' })
    });

    // Status-specific actions
    if (['pending', 'confirmed'].includes(booking.status)) {
      actions.push({
        id: 'reschedule',
        label: t('bookings.actions.reschedule', 'Reschedule'),
        icon: 'calendar-outline',
        color: colors.warning,
        action: (booking) => setModalData({ visible: true, booking, type: 'reschedule' })
      });

      actions.push({
        id: 'cancel',
        label: t('bookings.actions.cancel', 'Cancel'),
        icon: 'close-circle-outline',
        color: colors.error,
        action: (booking) => setModalData({ visible: true, booking, type: 'cancel' })
      });
    }

    // Rebook action for completed bookings
    if (booking.status === 'completed') {
      actions.push({
        id: 'rebook',
        label: t('bookings.actions.rebook', 'Book Again'),
        icon: 'repeat-outline',
        color: colors.primary,
        action: (booking) => handleRebook(booking)
      });
    }

    return actions;
  }, [colors, t]);

  // Enhanced booking action handlers
  const handleCancelBooking = async (booking: Booking) => {
    try {
      setLoading(true);
      await storeCancelBooking(booking.id);

      // Update local state immediately for better UX
      setBookings(prev => prev.map(b =>
        b.id === booking.id ? { ...b, status: 'cancelled' } : b
      ));

      showToast(t('bookings.messages.cancelSuccess', 'Booking cancelled successfully'), 'success');
      setModalData({ visible: false, booking: null, type: 'details' });

    } catch (error) {
      console.error('Failed to cancel booking:', error);
      showToast(
        t('bookings.messages.cancelError', 'Failed to cancel booking. Please try again.'),
        'error'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleRescheduleBooking = async (booking: Booking, newDate: Date, newTime: string) => {
    try {
      setLoading(true);
      // For now, just show a message - reschedule functionality would need backend support
      showToast('Reschedule functionality coming soon', 'info');
      setModalData({ visible: false, booking: null, type: 'details' });

    } catch (error) {
      console.error('Failed to reschedule booking:', error);
      showToast(
        t('bookings.messages.rescheduleError', 'Failed to reschedule booking. Please try again.'),
        'error'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleRebook = (booking: Booking) => {
    navigation.navigate('ServiceDetails', {
      serviceId: booking.serviceId,
      providerId: booking.providerId,
      rebook: true,
      previousBooking: booking
    });
  };

  const handleContactProvider = (booking: Booking) => {
    navigation.navigate('Chat', {
      providerId: booking.providerId,
      bookingId: booking.id,
      providerName: booking.providerName
    });
  };

  // handleMegaMenuNavigate removed - REC-RESP-001: Using thumb-friendly bottom navigation only

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return colors.success?.text || '#10B981';
      case 'pending':
        return colors.warning?.text || '#F59E0B';
      case 'completed':
        return colors.sage400;
      case 'cancelled':
        return colors.error?.text || '#EF4444';
      case 'in_progress':
        return colors.info?.text || '#3B82F6';
      default:
        return colors.text.secondary;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'checkmark-circle';
      case 'pending':
        return 'time';
      case 'completed':
        return 'checkmark-done-circle';
      case 'cancelled':
        return 'close-circle';
      case 'in_progress':
        return 'play-circle';
      default:
        return 'help-circle';
    }
  };

  // Enhanced filtering and search logic
  const filteredBookings = useMemo(() => {
    let filtered = [...bookings];

    // Apply status filter
    if (selectedFilter !== 'all') {
      if (selectedFilter === 'upcoming') {
        filtered = filtered.filter(booking =>
          ['pending', 'confirmed'].includes(booking.status)
        );
      } else {
        filtered = filtered.filter(booking => booking.status === selectedFilter);
      }
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(booking =>
        booking.serviceName.toLowerCase().includes(query) ||
        booking.providerName.toLowerCase().includes(query) ||
        booking.status.toLowerCase().includes(query) ||
        booking.location?.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [bookings, selectedFilter, searchQuery]);

  // Enhanced empty state logic
  const getEmptyStateConfig = () => {
    if (loading) {
      return null;
    }

    if (error) {
      return {
        icon: 'alert-circle-outline',
        title: t('bookings.empty.error.title', 'Unable to load bookings'),
        description: t('bookings.empty.error.description', 'Please check your connection and try again'),
        actionLabel: t('bookings.empty.error.action', 'Retry'),
        onAction: loadBookings
      };
    }

    if (searchQuery.trim() && filteredBookings.length === 0) {
      return {
        icon: 'search-outline',
        title: t('bookings.empty.search.title', 'No bookings found'),
        description: t('bookings.empty.search.description', 'Try adjusting your search terms'),
        actionLabel: t('bookings.empty.search.action', 'Clear Search'),
        onAction: () => setSearchQuery('')
      };
    }

    if (selectedFilter === 'all' && bookings.length === 0) {
      return {
        icon: 'calendar-outline',
        title: t('bookings.empty.all.title', 'No bookings yet'),
        description: t('bookings.empty.all.description', 'Start exploring services to make your first booking'),
        actionLabel: t('bookings.empty.all.action', 'Browse Services'),
        onAction: () => navigation.navigate('Search')
      };
    }

    if (filteredBookings.length === 0) {
      const filterName = filterTabs.find(tab => tab.id === selectedFilter)?.name || selectedFilter;
      return {
        icon: 'calendar-outline',
        title: t('bookings.empty.filter.title', `No ${filterName.toLowerCase()} bookings`),
        description: t('bookings.empty.filter.description', `You don't have any ${filterName.toLowerCase()} bookings`),
        actionLabel: t('bookings.empty.filter.action', 'View All Bookings'),
        onAction: () => setSelectedFilter('all')
      };
    }

    return null;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const renderFilterTab = ({ item }: { item: FilterTab }) => (
    <TouchableOpacity
      style={[
        styles.filterTab,
        selectedFilter === item.id && styles.activeFilterTab,
      ]}
      onPress={() => setSelectedFilter(item.id)}
      testID={`filter-${item.id}`}
      accessibilityLabel={`Filter by ${item.name}`}
      accessibilityRole="button">
      <Text
        style={[
          styles.filterTabText,
          selectedFilter === item.id && styles.activeFilterTabText,
        ]}>
        {item.name}
      </Text>
      {item.count !== undefined && (
        <View style={styles.countBadge}>
          <Text style={styles.countText}>{item.count}</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const renderBooking = ({ item }: { item: Booking }) => (
    <Card style={styles.bookingCard}>
      <View style={styles.bookingHeader}>
        <View style={styles.bookingImageContainer}>
          <StoreImage
            providerId={item.provider_id}
            providerName={item.provider_name}
            category={item.service_category}
            size="small"
            testID={`booking-image-${item.id}`}
          />
        </View>
        <View style={styles.bookingInfo}>
          <Text style={styles.providerName} numberOfLines={1}>
            {item.provider_name}
          </Text>
          <Text style={styles.serviceName} numberOfLines={1}>
            {item.service_name}
          </Text>
        </View>
        <View style={styles.statusContainer}>
          <Ionicons
            name={getStatusIcon(item.status) as any}
            size={16}
            color={getStatusColor(item.status)}
          />
          <Text
            style={[styles.statusText, { color: getStatusColor(item.status) }]}>
            {item.status_display}
          </Text>
        </View>
      </View>

      <View style={styles.bookingDetails}>
        <View style={styles.dateTimeContainer}>
          <View style={styles.dateTimeItem}>
            <Ionicons
              name="calendar-outline"
              size={16}
              color={colors.text.tertiary}
            />
            <Text style={styles.dateTimeText}>
              {formatDate(item.scheduled_datetime)}
            </Text>
          </View>
          <View style={styles.dateTimeItem}>
            <Ionicons
              name="time-outline"
              size={16}
              color={colors.text.tertiary}
            />
            <Text style={styles.dateTimeText}>
              {formatTime(item.scheduled_datetime)}
            </Text>
          </View>
        </View>

        <View style={styles.durationPriceContainer}>
          <Text style={styles.durationText}>{item.duration_display}</Text>
          <Text style={styles.priceText}>${item.total_amount}</Text>
        </View>
      </View>

      {item.customer_notes && (
        <View style={styles.notesContainer}>
          <Text style={styles.notesLabel}>Notes:</Text>
          <Text style={styles.notesText} numberOfLines={2}>
            {item.customer_notes}
          </Text>
        </View>
      )}

      <View style={styles.bookingActions}>
        {item.can_be_cancelled && (
          <Button
            onPress={() => handleCancelBooking(item)}
            variant="secondary"
            style={styles.actionButton}
            testID={`cancel-${item.id}`}>
            Cancel
          </Button>
        )}
        {item.can_be_rescheduled && (
          <Button
            onPress={() => handleRescheduleBooking(item)}
            variant="outline"
            style={styles.actionButton}
            testID={`reschedule-${item.id}`}>
            Reschedule
          </Button>
        )}
        {item.status === 'completed' && (
          <Button
            onPress={() => {
              navigation.navigate('LeaveReview', {
                bookingId: item.id,
                providerName: item.provider_name,
                serviceName: item.service_name,
              });
            }}
            variant="primary"
            style={styles.actionButton}
            testID={`review-${item.id}`}>
            Leave Review
          </Button>
        )}
      </View>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons
        name="calendar-outline"
        size={64}
        color={colors.text.tertiary}
      />
      <Text style={styles.emptyTitle}>{t('bookings.emptyState.title')}</Text>
      <Text style={styles.emptyDescription}>
        {selectedFilter === 'all'
          ? t('bookings.emptyState.description.all')
          : t('bookings.emptyState.description.filtered', { filter: selectedFilter })}
      </Text>
      <Button
        onPress={() => {
          /* TODO: Navigate to search */
        }}
        variant="primary"
        style={styles.bookNowButton}>
        {t('bookings.emptyState.action')}
      </Button>
    </View>
  );

  return (
    <SafeAreaScreen
      backgroundColor={colors.background.secondary}
      statusBarStyle="dark-content"
      respectNotch={true}
      respectGestures={true}
      testID="bookings-screen"
      accessibilityLabel="My bookings screen"
      accessibilityRole="main">
      {/* Header */}
      <View
        style={styles.header}
        accessibilityRole="header"
        accessibilityLabel="Bookings screen header">
        <View style={styles.menuButton}>
          {/* Menu button removed - using bottom navigation */}
        </View>
        <Text
          style={styles.title}
          accessibilityRole="header"
          accessibilityLevel={1}>
          {t('bookings.title')}
        </Text>
        <HeaderHelpButton
          size="medium"
          testID="bookings-help-button"
          accessibilityLabel="Get help"
          accessibilityHint="Double tap to access help and support options"
        />
      </View>

      {/* Filter Tabs */}
      <View style={styles.filtersContainer}>
        <FlatList
          data={filterTabs}
          renderItem={renderFilterTab}
          keyExtractor={item => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersList}
        />
      </View>

      {/* Bookings List */}
      {loading && bookings.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.sage400} />
          <Text style={styles.loadingText}>Loading bookings...</Text>
        </View>
      ) : (
        <FlatList
          data={bookings}
          renderItem={renderBooking}
          keyExtractor={item => item.id}
          style={styles.bookingsList}
          contentContainerStyle={styles.bookingsContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.sage400]}
              tintColor={colors.sage400}
            />
          }
          ListEmptyComponent={renderEmptyState}
        />
      )}

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Error loading bookings. Please try again.
          </Text>
        </View>
      )}

      {/* MegaMenu removed - using bottom navigation */}
    </SafeAreaScreen>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(20),
    paddingVertical: getResponsiveSpacing(16),
    backgroundColor: colors.surface || colors.background?.primary || colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: colors.sage100 || colors.border?.light || colors.border.light,
  },
  title: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: '700',
    color: colors.text || colors.text.primary,
  },
  menuButton: {
    padding: getResponsiveSpacing(8),
    borderRadius: getResponsiveSpacing(8),
    backgroundColor: 'transparent',
  },
  filtersContainer: {
    backgroundColor: colors.surface || colors.background?.primary || colors.background.primary,
    paddingVertical: getResponsiveSpacing(12),
    borderBottomWidth: 1,
    borderBottomColor: colors.sage100 || colors.border?.light || colors.border.light,
  },
  filtersList: {
    paddingHorizontal: getResponsiveSpacing(20),
  },
  filterTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(16),
    paddingVertical: getResponsiveSpacing(8),
    marginRight: getResponsiveSpacing(12),
    borderRadius: getResponsiveSpacing(20),
    backgroundColor: colors.background.secondary,
    borderWidth: 1,
    borderColor: colors.border.light,
    minHeight: getMinimumTouchTarget(),
  },
  activeFilterTab: {
    backgroundColor: colors.sage400,
    borderColor: colors.sage400,
  },
  filterTabText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '500',
    color: colors.text.secondary,
  },
  activeFilterTabText: {
    color: '#FFFFFF',
  },
  countBadge: {
    marginLeft: getResponsiveSpacing(6),
    paddingHorizontal: getResponsiveSpacing(6),
    paddingVertical: getResponsiveSpacing(2),
    borderRadius: getResponsiveSpacing(10),
    backgroundColor: colors.background.tertiary,
  },
  countText: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '600',
    color: colors.text.primary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(40),
  },
  loadingText: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
    marginTop: getResponsiveSpacing(16),
  },
  bookingsList: {
    flex: 1,
  },
  bookingsContent: {
    padding: getResponsiveSpacing(20),
  },
  bookingCard: {
    marginBottom: getResponsiveSpacing(16),
    padding: getResponsiveSpacing(16),
  },
  bookingHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: getResponsiveSpacing(12),
  },
  bookingImageContainer: {
    marginRight: getResponsiveSpacing(12),
  },
  bookingImagePlaceholder: {
    width: getResponsiveSpacing(50),
    height: getResponsiveSpacing(50),
    borderRadius: getResponsiveSpacing(8),
    backgroundColor: '#7C9A85',
    alignItems: 'center',
    justifyContent: 'center',
  },
  bookingImageText: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '700',
    color: '#FFFFFF',
  },
  bookingInfo: {
    flex: 1,
    marginRight: getResponsiveSpacing(12),
  },
  providerName: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: getResponsiveSpacing(4),
  },
  serviceName: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing(8),
    paddingVertical: getResponsiveSpacing(4),
    borderRadius: getResponsiveSpacing(12),
    backgroundColor: colors.background.tertiary,
  },
  statusText: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '500',
    marginLeft: getResponsiveSpacing(4),
  },
  bookingDetails: {
    marginBottom: getResponsiveSpacing(12),
  },
  dateTimeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: getResponsiveSpacing(8),
  },
  dateTimeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  dateTimeText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
    marginLeft: getResponsiveSpacing(6),
  },
  durationPriceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  durationText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.tertiary,
  },
  priceText: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: colors.sage400,
  },
  notesContainer: {
    marginBottom: getResponsiveSpacing(12),
    padding: getResponsiveSpacing(12),
    backgroundColor: colors.background.tertiary,
    borderRadius: getResponsiveSpacing(8),
  },
  notesLabel: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '500',
    color: colors.text.tertiary,
    marginBottom: getResponsiveSpacing(4),
  },
  notesText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.text.secondary,
    lineHeight: getResponsiveFontSize(20),
  },
  bookingActions: {
    flexDirection: 'row',
    gap: getResponsiveSpacing(8),
  },
  actionButton: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: getResponsiveSpacing(60),
    paddingHorizontal: getResponsiveSpacing(40),
  },
  emptyTitle: {
    fontSize: getResponsiveFontSize(20),
    fontWeight: '600',
    color: colors.text.primary,
    marginTop: getResponsiveSpacing(16),
    marginBottom: getResponsiveSpacing(8),
  },
  emptyDescription: {
    fontSize: getResponsiveFontSize(16),
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: getResponsiveFontSize(24),
    marginBottom: getResponsiveSpacing(24),
  },
  bookNowButton: {
    minWidth: 140,
  },
  errorContainer: {
    backgroundColor: colors.error?.background || '#FEE2E2',
    padding: getResponsiveSpacing(16),
    margin: getResponsiveSpacing(20),
    borderRadius: getResponsiveSpacing(8),
  },
  errorText: {
    fontSize: getResponsiveFontSize(14),
    color: colors.error?.text || '#DC2626',
    textAlign: 'center',
  },
});
