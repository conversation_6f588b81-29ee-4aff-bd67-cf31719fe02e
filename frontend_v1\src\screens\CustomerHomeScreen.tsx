import React, { useCallback, useMemo, useEffect } from 'react';
import { ScrollView, View, TouchableOpacity, FlatList, ActivityIndicator } from 'react-native';
import { StyleSheet, RefreshControl, SafeAreaView, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Import components
import { FocusableButton } from '../components/accessibility/FocusableButton';
import { Heading } from '../components/typography/Typography';

// Import hooks and services
import { useCustomerHomeData } from '../hooks/useCustomerHomeData';
import { useAuthStore } from '../store/authSlice';
import { useTheme } from '../contexts/ThemeContext';
import { useNavigationGuard } from '../hooks/useNavigationGuard';

// Import types and utilities
import { ScreenReaderUtils } from '../utils/accessibilityUtils';
import type { ServiceCategory, FeaturedProvider } from '../services/customerService';

const CustomerHomeScreen: React.FC = () => {
  const { colors } = useTheme();
  const { isAuthenticated, user } = useAuthStore();
  const { navigate } = useNavigationGuard();
  const {
    data,
    loading,
    error,
    refreshing,
    refresh,
  } = useCustomerHomeData();

  const styles = useMemo(() => createStyles(colors), [colors]);

  // Get greeting based on time of day
  const getGreeting = useCallback(() => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  }, []);

  const greeting = data.dashboard?.greeting || getGreeting();

  // Navigation handlers
  const handleCategoryPress = useCallback(async (category: ServiceCategory) => {
    console.log('Category pressed:', category.slug);
    await navigate('Search', { category: category.slug });
  }, [navigate]);

  const handleProviderPress = useCallback(async (provider: FeaturedProvider) => {
    console.log('Provider pressed:', provider.id);
    await navigate('ProviderDetails', { providerId: provider.id });
  }, [navigate]);

  const handleSeeAllPress = useCallback(async (section: string) => {
    console.log('See all pressed for:', section);
    switch (section) {
      case 'featured':
        await navigate('Search', { filter: 'featured' });
        break;
      case 'favorites':
        await navigate('Search', { filter: 'favorites' });
        break;
      case 'nearby':
        await navigate('Search', { filter: 'nearby' });
        break;
      case 'quick-book':
        await navigate('Bookings', { tab: 'quick-book' });
        break;
      default:
        await navigate('Search');
    }
  }, [navigate]);

  // Announce screen content for screen readers
  useEffect(() => {
    const announceScreenContent = async () => {
      const isScreenReaderEnabled = await ScreenReaderUtils.isScreenReaderEnabled();
      if (isScreenReaderEnabled && data.categories.length > 0) {
        // Delay announcement to allow screen to fully load
        setTimeout(() => {
          ScreenReaderUtils.announceForAccessibility(
            `Customer Home Screen loaded. ${greeting} ${user?.firstName || 'User'}. Browse ${data.categories.length} service categories including ${data.categories.map((c: ServiceCategory) => c.name).join(', ')}.`
          );
        }, 1000);
      }
    };

    announceScreenContent();
  }, [data.categories, greeting, user]);

  // Render functions
  const renderBrowseServicesSection = useCallback(() => {
    if (loading.categories && data.categories.length === 0) {
      return (
        <View style={styles.section}>
          <Heading level={2} color={colors.text.primary}>Browse Services</Heading>
          <ActivityIndicator size="large" color={colors.sage400} style={{ marginTop: 20 }} />
        </View>
      );
    }

    if (error.categories && data.categories.length === 0) {
      return (
        <View style={styles.section}>
          <Heading level={2} color={colors.text.primary}>Browse Services</Heading>
          <Text style={styles.errorText}>Failed to load categories. Pull to refresh.</Text>
        </View>
      );
    }

    return (
      <View style={styles.section} accessibilityRole="none" accessibilityLabel="Browse Services section">
        <View style={styles.sectionHeader}>
          <Heading level={2} color={colors.text.primary}>Browse Services</Heading>
        </View>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesScroll}
        >
          {data.categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[styles.categoryCard, { backgroundColor: category.color }]}
              onPress={() => handleCategoryPress(category)}
              accessibilityRole="button"
              accessibilityLabel={`${category.name} category, ${category.serviceCount} services`}
            >
              <Ionicons name={category.icon as any} size={24} color="#FFFFFF" />
              <Text style={styles.categoryName}>{category.name}</Text>
              <Text style={styles.categoryCount}>{category.serviceCount} services</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  }, [data.categories, loading.categories, error.categories, colors, handleCategoryPress]);

  const renderFeaturedProvidersSection = useCallback(() => {
    return (
      <View style={styles.section} accessibilityRole="none" accessibilityLabel="Featured Providers section">
        <View style={styles.sectionHeader}>
          <Heading level={2} color={colors.text.primary}>Featured Providers</Heading>
          <FocusableButton
            title="See All"
            onPress={() => handleSeeAllPress('featured')}
            variant="ghost"
            size="small"
            accessibilityLabel="See all featured providers"
          />
        </View>
        {loading.featuredProviders ? (
          <ActivityIndicator size="large" color={colors.sage400} style={{ marginTop: 20 }} />
        ) : error.featuredProviders ? (
          <Text style={styles.errorText}>Failed to load featured providers</Text>
        ) : data.featuredProviders.length > 0 ? (
          <FlatList
            horizontal
            data={data.featuredProviders}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => renderProviderCard(item)}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesScroll}
          />
        ) : (
          <Text style={styles.placeholderText}>No featured providers available</Text>
        )}
      </View>
    );
  }, [data.featuredProviders, loading.featuredProviders, error.featuredProviders, colors, handleSeeAllPress]);

  const renderFavoriteProvidersSection = useCallback(() => {
    return (
      <View style={styles.section} accessibilityRole="none" accessibilityLabel="Favorite Providers section">
        <View style={styles.sectionHeader}>
          <Heading level={2} color={colors.text.primary}>Favorite Providers</Heading>
          <FocusableButton
            title="See All"
            onPress={() => handleSeeAllPress('favorites')}
            variant="ghost"
            size="small"
            accessibilityLabel="See all favorite providers"
          />
        </View>
        {loading.favoriteProviders ? (
          <ActivityIndicator size="large" color={colors.sage400} style={{ marginTop: 20 }} />
        ) : error.favoriteProviders ? (
          <Text style={styles.placeholderText}>Failed to load favorite providers</Text>
        ) : data.favoriteProviders.length > 0 ? (
          <FlatList
            horizontal
            data={data.favoriteProviders}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => renderProviderCard(item)}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesScroll}
          />
        ) : (
          <Text style={styles.placeholderText}>No favorite providers yet</Text>
        )}
      </View>
    );
  }, [data.favoriteProviders, loading.favoriteProviders, error.favoriteProviders, colors, handleSeeAllPress]);

  const renderNearbyProvidersSection = useCallback(() => {
    return (
      <View style={styles.section} accessibilityRole="none" accessibilityLabel="Nearby Providers section">
        <View style={styles.sectionHeader}>
          <Heading level={2} color={colors.text.primary}>Nearby Providers</Heading>
          <FocusableButton
            title="See All"
            onPress={() => handleSeeAllPress('nearby')}
            variant="ghost"
            size="small"
            accessibilityLabel="See all nearby providers"
          />
        </View>
        {loading.nearbyProviders ? (
          <ActivityIndicator size="large" color={colors.sage400} style={{ marginTop: 20 }} />
        ) : error.nearbyProviders ? (
          <Text style={styles.placeholderText}>Failed to load nearby providers</Text>
        ) : data.nearbyProviders.length > 0 ? (
          <FlatList
            horizontal
            data={data.nearbyProviders}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => renderProviderCard(item)}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesScroll}
          />
        ) : (
          <Text style={styles.placeholderText}>No nearby providers found</Text>
        )}
      </View>
    );
  }, [data.nearbyProviders, loading.nearbyProviders, error.nearbyProviders, colors, handleSeeAllPress]);

  const renderProviderCard = useCallback((provider: FeaturedProvider) => {
    return (
      <TouchableOpacity
        key={provider.id}
        style={styles.providerCard}
        onPress={() => handleProviderPress(provider)}
        accessibilityRole="button"
        accessibilityLabel={`${provider.name}, ${provider.rating} stars, ${provider.reviewCount} reviews`}
      >
        <View style={styles.providerImageContainer}>
          {provider.avatar ? (
            <Text style={styles.providerInitials}>
              {provider.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </Text>
          ) : (
            <Ionicons name="person-outline" size={24} color={colors.text.secondary} />
          )}
        </View>
        <View style={styles.providerInfo}>
          <Text style={styles.providerName} numberOfLines={1}>{provider.name}</Text>
          <View style={styles.providerRating}>
            <Ionicons name="star" size={12} color="#FFD700" />
            <Text style={styles.ratingText}>{provider.rating.toFixed(1)}</Text>
            <Text style={styles.reviewCount}>({provider.reviewCount})</Text>
          </View>
          <Text style={styles.providerLocation} numberOfLines={1}>
            {provider.location.city}
          </Text>
        </View>
      </TouchableOpacity>
    );
  }, [colors, handleProviderPress]);

  const renderRecentBookingsSection = useCallback(() => {
    return (
      <View style={styles.section} accessibilityRole="none" accessibilityLabel="Recent Bookings and Quick Booking section">
        <View style={styles.sectionHeader}>
          <Heading level={2} color={colors.text.primary}>Recent Bookings</Heading>
          <FocusableButton
            title="Quick Book"
            onPress={() => handleSeeAllPress('quick-book')}
            variant="primary"
            size="small"
            accessibilityLabel="Quick booking"
          />
        </View>
        <View style={styles.section}>
          <Text style={styles.placeholderText}>Your recent bookings and quick booking options will appear here</Text>
        </View>
      </View>
    );
  }, [colors, handleSeeAllPress]);

  console.log('🏠 CustomerHomeScreen rendering... v2');
  console.log('🎨 Colors:', colors);
  console.log('📱 Categories:', data.categories.length);
  console.log('🔄 Loading:', loading.overall);

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
      accessibilityRole="none"
      accessibilityLabel="Customer Home Screen"
    >
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={refresh}
            colors={[colors.sage400 || '#5A7A63']}
            tintColor={colors.sage400 || '#5A7A63'}
            accessibilityLabel={refreshing ? "Refreshing content" : "Pull to refresh"}
          />
        }
        showsVerticalScrollIndicator={false}
        accessibilityRole="scrollbar"
        accessibilityLabel="Main content area"
        accessibilityHint="Scroll to browse services and providers"
      >
        {/* Welcome Section */}
        <View style={styles.welcomeSection} accessibilityRole="none" accessibilityLabel="Welcome section">
          <Text style={styles.greeting}>{greeting}</Text>
          <Text style={styles.userName}>
            Welcome{isAuthenticated && user?.firstName ? `, ${user.firstName}` : ' to Vierla'}
          </Text>
        </View>

        {/* Browse Services Section - positioned right under header */}
        {renderBrowseServicesSection()}

        {/* Featured Providers Section - at top of provider sections */}
        {renderFeaturedProvidersSection()}

        {/* Favorite Providers Section - second in provider sections */}
        {isAuthenticated && renderFavoriteProvidersSection()}

        {/* Nearby Providers Section - third in provider sections */}
        {renderNearbyProvidersSection()}

        {/* Recent Bookings merged with Quick Booking Section */}
        {isAuthenticated && renderRecentBookingsSection()}


      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors?.background?.primary || '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
    paddingHorizontal: 16,
  },
  welcomeSection: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    backgroundColor: colors?.background?.secondary || '#F8F9FA',
    borderRadius: 12,
    marginBottom: 24,
  },
  greeting: {
    fontSize: 16,
    color: colors?.text?.secondary || '#666',
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors?.text?.primary || '#333',
    fontFamily: 'Inter-Bold',
    marginTop: 4,
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 0,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors?.text?.primary || '#333',
    fontFamily: 'Inter-Bold',
  },
  seeAllText: {
    fontSize: 14,
    color: colors?.primary?.default || '#5A7A63',
    fontFamily: 'Inter-Medium',
    fontWeight: '500',
  },
  categoriesScroll: {
    paddingLeft: 0,
    paddingRight: 16,
  },
  categoryCard: {
    width: 120,
    height: 100,
    borderRadius: 12,
    padding: 12,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryName: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 8,
    textAlign: 'center',
  },
  categoryCount: {
    color: '#FFFFFF',
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
    opacity: 0.9,
  },
  placeholderText: {
    fontSize: 16,
    color: colors?.text?.tertiary || '#999',
    textAlign: 'center',
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  errorText: {
    fontSize: 14,
    color: colors?.error || '#FF6B6B',
    textAlign: 'center',
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  providerCard: {
    width: 160,
    backgroundColor: colors?.background?.secondary || '#F8F9FA',
    borderRadius: 12,
    padding: 12,
    marginRight: 12,
    borderWidth: 1,
    borderColor: colors?.border || '#E5E5E5',
  },
  providerImageContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors?.sage100 || '#E8F5E8',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  providerInitials: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors?.sage600 || '#5A7A63',
  },
  providerInfo: {
    flex: 1,
  },
  providerName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors?.text?.primary || '#333',
    marginBottom: 4,
  },
  providerRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  ratingText: {
    fontSize: 12,
    color: colors?.text?.primary || '#333',
    marginLeft: 4,
    fontWeight: '500',
  },
  reviewCount: {
    fontSize: 12,
    color: colors?.text?.secondary || '#666',
    marginLeft: 2,
  },
  providerLocation: {
    fontSize: 12,
    color: colors?.text?.secondary || '#666',
  },
  seeAllButton: {
    backgroundColor: 'transparent',
    minHeight: 32,
    paddingHorizontal: 8,
  },
});

export default CustomerHomeScreen;