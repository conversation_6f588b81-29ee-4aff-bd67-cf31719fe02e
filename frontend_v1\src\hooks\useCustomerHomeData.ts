/**
 * Customer Home Data Hook - Centralized data management for home screen
 *
 * Hook Contract:
 * - Manages all data fetching for customer home screen
 * - Provides loading states and error handling
 * - Implements caching and refresh functionality
 * - Supports offline mode with fallback data
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import customerService, {
  ServiceCategory,
  FeaturedProvider,
  NearbyProvider,
  CustomerDashboard
} from '../services/customerService';
import { useAuthStore } from '../store/authSlice';
import cacheService from '../services/cacheService';
import { performanceMonitor } from '../services/performanceMonitor';

interface HomeScreenData {
  categories: ServiceCategory[];
  featuredProviders: FeaturedProvider[];
  favoriteProviders: FeaturedProvider[];
  nearbyProviders: NearbyProvider[];
  dashboard: CustomerDashboard | null;
  recommendations: FeaturedProvider[];
}

interface HomeScreenState {
  data: HomeScreenData;
  loading: {
    categories: boolean;
    featuredProviders: boolean;
    favoriteProviders: boolean;
    nearbyProviders: boolean;
    dashboard: boolean;
    recommendations: boolean;
    overall: boolean;
  };
  error: {
    categories: string | null;
    featuredProviders: string | null;
    favoriteProviders: string | null;
    nearbyProviders: string | null;
    dashboard: string | null;
    recommendations: string | null;
    overall: string | null;
  };
  lastUpdated: Date | null;
  refreshing: boolean;
}

const initialData: HomeScreenData = {
  categories: [],
  featuredProviders: [],
  favoriteProviders: [],
  nearbyProviders: [],
  dashboard: null,
  recommendations: [],
};

const initialLoading = {
  categories: false,
  featuredProviders: false,
  favoriteProviders: false,
  nearbyProviders: false,
  dashboard: false,
  recommendations: false,
  overall: false,
};

const initialError = {
  categories: null,
  featuredProviders: null,
  favoriteProviders: null,
  nearbyProviders: null,
  dashboard: null,
  recommendations: null,
  overall: null,
};

export const useCustomerHomeData = () => {
  const { isAuthenticated, user } = useAuthStore();
  const [state, setState] = useState<HomeScreenState>({
    data: initialData,
    loading: initialLoading,
    error: initialError,
    lastUpdated: null,
    refreshing: false,
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const cacheRef = useRef<{ [key: string]: { data: any; timestamp: number } }>({});
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  // Helper function to check cache validity
  const isCacheValid = useCallback((key: string): boolean => {
    const cached = cacheRef.current[key];
    if (!cached) return false;
    return Date.now() - cached.timestamp < CACHE_DURATION;
  }, []);

  // Helper function to get cached data
  const getCachedData = useCallback((key: string) => {
    const cached = cacheRef.current[key];
    return cached ? cached.data : null;
  }, []);

  // Helper function to set cache
  const setCache = useCallback((key: string, data: any) => {
    cacheRef.current[key] = {
      data,
      timestamp: Date.now(),
    };
  }, []);

  // Update loading state for specific data type
  const setLoading = useCallback((key: keyof typeof initialLoading, value: boolean) => {
    setState(prev => ({
      ...prev,
      loading: {
        ...prev.loading,
        [key]: value,
        overall: value || Object.values({ ...prev.loading, [key]: value }).some(Boolean),
      },
    }));
  }, []);

  // Update error state for specific data type
  const setError = useCallback((key: keyof typeof initialError, value: string | null) => {
    setState(prev => ({
      ...prev,
      error: {
        ...prev.error,
        [key]: value,
      },
    }));
  }, []);

  // Update data for specific type
  const setData = useCallback((key: keyof HomeScreenData, value: any) => {
    setState(prev => ({
      ...prev,
      data: {
        ...prev.data,
        [key]: value,
      },
      lastUpdated: new Date(),
    }));
  }, []);

  // Fetch service categories with advanced caching and performance monitoring
  const fetchCategories = useCallback(async () => {
    const cacheKey = 'customer_home_categories';
    const startTime = Date.now();

    setLoading('categories', true);
    setError('categories', null);

    try {
      // Try to get from cache first
      const cachedData = await cacheService.get<ServiceCategory[]>(cacheKey);

      if (cachedData) {
        setData('categories', cachedData);
        setLoading('categories', false);

        // Track cache hit
        performanceMonitor.trackNetworkRequest(
          '/api/v1/catalog/categories/',
          'GET',
          Date.now() - startTime,
          200,
          0,
          0,
          true // cached
        );

        return;
      }

      // Fetch from API if not in cache
      const categories = await customerService.getServiceCategories();

      // Track API request
      performanceMonitor.trackNetworkRequest(
        '/api/v1/catalog/categories/',
        'GET',
        Date.now() - startTime,
        200,
        0,
        0,
        false // not cached
      );

      // Store in cache
      await cacheService.set(cacheKey, categories, 5 * 60 * 1000); // 5 minutes TTL

      setData('categories', categories);
    } catch (error: any) {
      console.error('Failed to fetch categories:', error);
      setError('categories', error.message || 'Failed to load categories');

      // Track error
      performanceMonitor.trackNetworkRequest(
        '/api/v1/catalog/categories/',
        'GET',
        Date.now() - startTime,
        error.status || 500,
        0,
        0,
        false
      );
    } finally {
      setLoading('categories', false);
    }
  }, [setData, setLoading, setError]);

  // Fetch featured providers with caching and performance monitoring
  const fetchFeaturedProviders = useCallback(async () => {
    const cacheKey = 'customer_home_featured_providers';
    const startTime = Date.now();

    setLoading('featuredProviders', true);
    setError('featuredProviders', null);

    try {
      // Try cache first
      const cachedData = await cacheService.get<FeaturedProvider[]>(cacheKey);

      if (cachedData) {
        setData('featuredProviders', cachedData);
        setLoading('featuredProviders', false);

        performanceMonitor.trackNetworkRequest(
          '/api/v1/catalog/providers/featured/',
          'GET',
          Date.now() - startTime,
          200,
          0,
          0,
          true
        );

        return;
      }

      // Fetch from API
      const providers = await customerService.getFeaturedProviders(10);

      performanceMonitor.trackNetworkRequest(
        '/api/v1/catalog/providers/featured/',
        'GET',
        Date.now() - startTime,
        200,
        0,
        0,
        false
      );

      // Cache the result
      await cacheService.set(cacheKey, providers, 10 * 60 * 1000); // 10 minutes TTL

      setData('featuredProviders', providers);
    } catch (error: any) {
      console.error('Failed to fetch featured providers:', error);
      setError('featuredProviders', error.message || 'Failed to load featured providers');

      performanceMonitor.trackNetworkRequest(
        '/api/v1/catalog/providers/featured/',
        'GET',
        Date.now() - startTime,
        error.status || 500,
        0,
        0,
        false
      );
    } finally {
      setLoading('featuredProviders', false);
    }
  }, [setData, setLoading, setError]);

  // Fetch favorite providers
  const fetchFavoriteProviders = useCallback(async () => {
    if (!isAuthenticated) return;

    const cacheKey = 'favoriteProviders';
    
    if (isCacheValid(cacheKey)) {
      setData('favoriteProviders', getCachedData(cacheKey));
      return;
    }

    setLoading('favoriteProviders', true);
    setError('favoriteProviders', null);

    try {
      const providers = await customerService.getFavoriteProviders();
      setData('favoriteProviders', providers);
      setCache(cacheKey, providers);
    } catch (error: any) {
      console.error('Failed to fetch favorite providers:', error);
      setError('favoriteProviders', error.message || 'Failed to load favorite providers');
    } finally {
      setLoading('favoriteProviders', false);
    }
  }, [isAuthenticated, isCacheValid, getCachedData, setData, setCache, setLoading, setError]);

  // Fetch nearby providers
  const fetchNearbyProviders = useCallback(async () => {
    // For now, use default coordinates (Ottawa)
    const defaultLat = 45.4215;
    const defaultLng = -75.6972;

    const cacheKey = 'nearbyProviders';
    
    if (isCacheValid(cacheKey)) {
      setData('nearbyProviders', getCachedData(cacheKey));
      return;
    }

    setLoading('nearbyProviders', true);
    setError('nearbyProviders', null);

    try {
      const providers = await customerService.getNearbyProviders(defaultLat, defaultLng, 10, 10);
      setData('nearbyProviders', providers);
      setCache(cacheKey, providers);
    } catch (error: any) {
      console.error('Failed to fetch nearby providers:', error);
      setError('nearbyProviders', error.message || 'Failed to load nearby providers');
    } finally {
      setLoading('nearbyProviders', false);
    }
  }, [isCacheValid, getCachedData, setData, setCache, setLoading, setError]);

  // Fetch dashboard data
  const fetchDashboard = useCallback(async () => {
    if (!isAuthenticated) return;

    const cacheKey = 'dashboard';
    
    if (isCacheValid(cacheKey)) {
      setData('dashboard', getCachedData(cacheKey));
      return;
    }

    setLoading('dashboard', true);
    setError('dashboard', null);

    try {
      const dashboard = await customerService.getCustomerDashboard();
      setData('dashboard', dashboard);
      setCache(cacheKey, dashboard);
    } catch (error: any) {
      console.error('Failed to fetch dashboard:', error);
      setError('dashboard', error.message || 'Failed to load dashboard');
    } finally {
      setLoading('dashboard', false);
    }
  }, [isAuthenticated, isCacheValid, getCachedData, setData, setCache, setLoading, setError]);

  // Fetch personalized recommendations
  const fetchRecommendations = useCallback(async () => {
    if (!isAuthenticated) return;

    const cacheKey = 'recommendations';
    
    if (isCacheValid(cacheKey)) {
      setData('recommendations', getCachedData(cacheKey));
      return;
    }

    setLoading('recommendations', true);
    setError('recommendations', null);

    try {
      const recommendations = await customerService.getPersonalizedRecommendations();
      setData('recommendations', recommendations);
      setCache(cacheKey, recommendations);
    } catch (error: any) {
      console.error('Failed to fetch recommendations:', error);
      setError('recommendations', error.message || 'Failed to load recommendations');
    } finally {
      setLoading('recommendations', false);
    }
  }, [isAuthenticated, isCacheValid, getCachedData, setData, setCache, setLoading, setError]);

  // Load all data
  const loadAllData = useCallback(async () => {
    // Cancel any ongoing requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();

    // Load data in parallel
    await Promise.allSettled([
      fetchCategories(),
      fetchFeaturedProviders(),
      fetchFavoriteProviders(),
      fetchNearbyProviders(),
      fetchDashboard(),
      fetchRecommendations(),
    ]);
  }, [
    fetchCategories,
    fetchFeaturedProviders,
    fetchFavoriteProviders,
    fetchNearbyProviders,
    fetchDashboard,
    fetchRecommendations,
  ]);

  // Refresh all data
  const refresh = useCallback(async () => {
    setState(prev => ({ ...prev, refreshing: true }));
    
    // Clear cache
    cacheRef.current = {};
    
    try {
      await loadAllData();
    } finally {
      setState(prev => ({ ...prev, refreshing: false }));
    }
  }, [loadAllData]);

  // Initial data load
  useEffect(() => {
    loadAllData();

    // Cleanup on unmount
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [loadAllData]);

  return {
    ...state,
    refresh,
    loadAllData,
    fetchCategories,
    fetchFeaturedProviders,
    fetchFavoriteProviders,
    fetchNearbyProviders,
    fetchDashboard,
    fetchRecommendations,
  };
};
